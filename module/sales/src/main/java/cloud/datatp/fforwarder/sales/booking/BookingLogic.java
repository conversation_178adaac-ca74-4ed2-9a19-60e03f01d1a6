package cloud.datatp.fforwarder.sales.booking;

import cloud.datatp.bfsone.partner.BFSOnePartnerLogic;
import cloud.datatp.bfsone.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.booking.dto.SellingRate;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;
import cloud.datatp.fforwarder.sales.booking.entity.BookingAttachment;
import cloud.datatp.fforwarder.sales.booking.repository.BookingAttachmentRepository;
import cloud.datatp.fforwarder.sales.booking.repository.BookingRepository;
import cloud.datatp.fforwarder.sales.common.entity.CustomerChargeModel;
import cloud.datatp.fforwarder.sales.inquiry.InquiryLogic;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.integration.BFSOneCRMLogic;
import cloud.datatp.fforwarder.sales.quotation.SpecificQuotationLogic;
import cloud.datatp.fforwarder.settings.Purpose;
import cloud.datatp.fforwarder.settings.template.NameFeeDescLogic;
import cloud.datatp.fforwarder.settings.template.entity.NameFeeDescription;
import cloud.datatp.fforwarder.settings.template.entity.NameFeeDescription.Type;
import jakarta.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.project.task.entity.TaskAttachment;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class BookingLogic extends DAOService {

  @Autowired
  private BookingRepository bookingRepo;

  @Autowired
  private InquiryLogic inquiryLogic;

  @Autowired
  private InquiryRequestLogic requestLogic;

  @Autowired
  private SpecificQuotationLogic specificQuotationLogic;

  @Autowired
  private IStorageService storageService;

  @Autowired
  private BookingAttachmentRepository attachmentRepo;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private SeqService seqService;

  @Autowired
  private PartnerLogic partnerLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private BFSOnePartnerLogic bfsOnePartnerLogic;

  @Autowired
  private NameFeeDescLogic nameFeeDescLogic;

  @Autowired
  private BFSOneCRMLogic bfsOneCRMLogic;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(Booking.HPH_SEQUENCE, 10);
    seqService.createIfNotExists(Booking.HAN_SEQUENCE, 10);
    seqService.createIfNotExists(Booking.HCM_SEQUENCE, 10);
    seqService.createIfNotExists(Booking.DAD_SEQUENCE, 10);
    seqService.createIfNotExists(Booking.CORP_SEQUENCE, 10);
  }

  public BookingModel getBookingModel(ClientInfo client, Company company, Long bookingId) {
    Booking booking = getBooking(client, company, bookingId);
    Objects.assertNotNull(booking, "Booking is not found!!!, id = " + bookingId);
    BookingModel model = new BookingModel(booking);

    List<SellingRate> sellingRates = model.getSellingRates();
    if (sellingRates != null) {
      Long clientPartnerId = model.getClientPartnerId();
      BFSOnePartner partner = bfsOnePartnerLogic.getById(client, company, clientPartnerId);
      Objects.assertNotNull(partner, "Client {0} is not found", clientPartnerId);
      for (SellingRate sellingRate : sellingRates) {
        if (sellingRate.getPayerPartnerId() == null) {
          sellingRate.setPayerPartnerId(clientPartnerId);
          sellingRate.setPayerPartnerLabel(partner.getLabel());
          sellingRate.setPayerPartnerCode(partner.getBfsonePartnerCode());
        }
      }
    }
    return model;
  }

  public BookingModel saveBookingModel(ClientInfo client, Company company, BookingModel bookingModel) {
    Long clientPartnerId = bookingModel.getClientPartnerId();
    Objects.assertNotNull(clientPartnerId, "Client must be not null");

    // Check and set payer partner for selling rates
    List<SellingRate> sellingRates = bookingModel.getSellingRates();
    if (sellingRates != null) {
      BFSOnePartner partner = bfsOnePartnerLogic.getById(client, company, clientPartnerId);
      Objects.assertNotNull(partner, "Client {0} is not found", clientPartnerId);

      for (SellingRate sellingRate : sellingRates) {
        if (sellingRate.getPayerPartnerId() == null) {
          sellingRate.setPayerPartnerId(clientPartnerId);
          sellingRate.setPayerPartnerLabel(partner.getLabel());
          sellingRate.setPayerPartnerCode(partner.getBfsonePartnerCode());
        }
        if (StringUtil.isEmpty(sellingRate.getPayerPartnerCode())) {
          final Long payerPartnerId = sellingRate.getPayerPartnerId();
          BFSOnePartner byId = bfsOnePartnerLogic.getById(client, company, sellingRate.getPayerPartnerId());
          Objects.assertNotNull(byId, "Client {0} is not found", payerPartnerId);
          sellingRate.setPayerPartnerCode(byId.getBfsonePartnerCode());
          sellingRate.setPayerPartnerLabel(byId.getName());
        }
      }
    }

    Booking booking = null;
    if (bookingModel.getId() != null) {
      booking = getBooking(client, company, bookingModel.getId());
    }
    if (booking == null) {
      booking = new Booking();
      booking.setCustomerChargeModel(new CustomerChargeModel());
    }

    booking = bookingModel.toBooking(booking);
    Booking updateBooking = saveBooking(client, company, booking);
    bookingModel.setId(updateBooking.getId());
    return bookingModel;
  }

  public BookingModel newBookingModel(ClientInfo client, Company company, BookingModel template) {

    if (template.getSenderAccountId() == null) {
      Account account = accountLogic.getAccountById(client, client.getAccountId());
      Objects.assertNotNull(account, "Employee is not found, login id = " + client.getRemoteUser());
      template.setSenderAccountId(account.getId());
      template.setSenderLabel(account.getFullName());
    }

    BFSOnePartner customerPartner = bfsOnePartnerLogic.getById(client, company, template.getClientPartnerId());

    if (customerPartner != null) {
      template.setClientLabel(customerPartner.getName());
      String printConfirmBillInfo = customerPartner.getPrintCustomConfirmBillInfo();
      SpecificServiceInquiry inquiry = template.getInquiry();
      final Purpose purpose = inquiry.getPurpose();
      if (Purpose.IMPORT == purpose) {
        template.getShipmentInfo().setConsigneeLabel(printConfirmBillInfo);
        template.getShipmentInfo().setConsigneePartnerId(customerPartner.getId());
      } else {
        template.getShipmentInfo().setShipperLabel(printConfirmBillInfo);
        template.getShipmentInfo().setShipperPartnerId(customerPartner.getId());
      }
    }

    //TODO: Ensure selling has code match with bfsone
    List<SellingRate> sellingRates = template.getSellingRates();
    List<SellingRate> filtered = new ArrayList<>();
    for (SellingRate sel : sellingRates) {

      if (customerPartner != null && StringUtil.isEmpty(sel.getPayerPartnerCode())) {
        sel.setPayerPartnerCode(customerPartner.getBfsonePartnerCode());
        sel.setPayerPartnerId(customerPartner.getId());
        sel.setPayerPartnerLabel(customerPartner.getName());
      }

      if (StringUtil.isEmpty(sel.getCode())) {
        List<NameFeeDescription> nameFees = nameFeeDescLogic.findByTypeAndLabel(client, company, Type.SELLING, sel.getName());
        NameFeeDescription first = nameFees.stream().findFirst().orElse(null);
        if (first != null) {
          sel.setCode(first.getCode());
          filtered.add(sel);
        }
      } else {
        filtered.add(sel);
      }

    }
    template.setSellingRates(filtered);
    return template;
  }

  public Booking getBooking(ClientInfo client, Company company, Long id) {
    return bookingRepo.findById(id).orElse(null);
  }

  public Booking getByBookingProcessId(ClientInfo client, Company company, Long processId) {
    return bookingRepo.getByProcessId(processId);
  }

  public Booking getBookingByCaseRef(ClientInfo client, Company company, String caseRef) {
    return bookingRepo.getByCaseRef(company.getId(), caseRef);
  }

  public String genBookingCode(ClientInfo client, Company company) {
    final String yyMM = new SimpleDateFormat("yyMM").format(new Date());
    String prefix = "IB" + yyMM + company.getId() + client.getAccountId();
    return prefix + String.format("%06d", seqService.nextSequence(Booking.getSequence(company.getCode())));
  }

  public Booking saveBooking(ClientInfo client, Company company, Booking booking) {
    booking.set(client, company);
    booking = bookingRepo.save(booking);
    return booking;
  }

  public List<SqlMapRecord> searchBookings(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return new ArrayList<>();
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    if (permission.isGroupScope()) {
      String coreScriptDir = appEnv.addonPath("core", "groovy");
      String coreScriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchDbRecords(client, coreScriptDir, coreScriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("salemanAccountIds", participantAccountIds);
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/BookingSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchBooking", sqlParams);
  }

  public boolean changeBookingStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    bookingRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public int deleteBookings(ClientInfo client, Company company, List<Long> bookingIds) {
    for (Long bookingId : bookingIds) {
      Booking booking = getBooking(client, company, bookingId);
      if (booking == null) continue;
      String caseReference = booking.getExternalCaseReference();
      if (StringUtil.isNotEmpty(caseReference)) {
        bfsOneCRMLogic.deleteInternalBooking(client, company, caseReference);
      }
    }

    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), Booking.class, bookingIds);
    int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();

    return count;
  }

  public List<BookingAttachment> saveBookingAttachments(
    ClientInfo client, Company company, String requestReference, List<BookingAttachment> attachments) {
    //Booking booking = getBooking(client, company, requestReference);
    //TODO: Dan - review this method;
    Booking booking = null;
    SpecificServiceInquiry inquiry = booking.getInquiry();
    Long clientPartnerId = inquiry.getClientPartnerId();
    String storagePath = BookingAttachment.getBookingStoragePath(clientPartnerId + "", requestReference);
    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    storage.saveAttachments(storagePath, attachments, true);
    for (BookingAttachment attachment : attachments) {
      attachment.setBookingCode(requestReference);
      attachment.set(client, company.getId());
    }
    attachments = attachmentRepo.saveAll(attachments);
    List<Long> idSet = TaskAttachment.getIds(attachments);
    attachmentRepo.deleteOrphan(company.getId(), inquiry.getReferenceCode(), idSet);
    return attachments;
  }

  public List<BookingAttachment> findBookingAttachments(ClientInfo client, Company company, String bookingCode) {
    return attachmentRepo.findByBookingCode(company.getId(), bookingCode);
  }

}