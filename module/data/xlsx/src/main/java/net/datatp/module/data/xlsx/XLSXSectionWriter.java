package net.datatp.module.data.xlsx;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.xlsx.HeaderMapping.XLSXHeader;
import net.datatp.module.data.xlsx.export.DataListExportModel.SelectField;
import net.datatp.module.data.xlsx.export.DataListExportModel.XLSXBlockTemplate;
import net.datatp.module.data.xlsx.export.DataListExportModel.XLSXCellTemplate;
import net.datatp.module.data.xlsx.export.DataListExportModel.XLSXRowTemplate;
import net.datatp.module.data.xlsx.export.ExportStyleBuilder;
import net.datatp.module.data.xlsx.export.ExportStyleBuilder.StyleConfig;
import net.datatp.module.data.xlsx.export.XLSXExportHelper;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;

@Getter @Slf4j
public class XLSXSectionWriter {
  private XLSXSheetWriter sheetWriter;
  private HeaderMapping headerMapping;
  private Row currentRow;

  public XLSXSectionWriter(XLSXSheetWriter sheetWriter) {
    this.sheetWriter = sheetWriter;
    this.nextRow();
  }

  public void writeHeader(String ... header) {
    if(headerMapping != null) {
      throw RuntimeError.IllegalState("Section header is already created");
    }
    headerMapping = new HeaderMapping(header);
    currentRow = nextRow();
    final Map<String, XLSXHeader> headerMap = headerMapping.getHeaderMap();
    for (Entry<String, XLSXHeader> entry : headerMap.entrySet()) {
      final XLSXHeader xlsxHeader = entry.getValue();
      final int colIdx = xlsxHeader.getColIdx();
      Cell cell = currentRow.createCell(colIdx);
      cell.setCellValue(entry.getKey());
    }
  }

  public void writeHeader(List<String> headers) {
    writeHeader(headers.toArray(new String[0]));
  }

  public void writeHeader(CellStyle headingStyle, List<String> headers) {
    writeHeader(headers.toArray(new String[0]));
    final Iterator<Cell> cellIterator = currentRow.cellIterator();
    final short lastCellNum = currentRow.getLastCellNum();
    for (int i = 0; i < lastCellNum; i++) {
      final Cell cell = currentRow.getCell(i);
      cell.setCellStyle(headingStyle);
      sheetWriter.getSheet().setColumnWidth(i, 20 * 256);
    }
  }

  public void writeCells(Object ... cells) {
    sheetWriter.writeCells(cells);
  }

  public void writeCell(Row row, String col, Object cellVal) {
    if(headerMapping != null) {
      throw RuntimeError.IllegalState("Section header is not created");
    }
    if(cellVal == null) return;
    int colIdx = headerMapping.getColumnIndex(col);
    Cell cell = row.createCell(colIdx);
    cell.setCellValue(cellVal.toString());
  }

  public Row nextRow() {
    currentRow = sheetWriter.nextRow();
    return currentRow;
  }

  public void skipRow(int num) { sheetWriter.skipRow(num); }

  public void writeBlockTemplate(ExportStyleBuilder styleBuilder, XLSXBlockTemplate template) {
    if(template == null) return;
    Workbook workbook = sheetWriter.getSheet().getWorkbook();
    List<XLSXRowTemplate> rows = template.getRows();
    for (XLSXRowTemplate row : rows) {
      String dataType = row.getDataType();
      if ("blank".equals(dataType)) {
        skipRow(currentRow.getRowNum() + row.getRowspan());
      } else if("primitive".equals(dataType)) {
        writePrimitiveRow(row, styleBuilder, workbook);
      } else if("complex".equals(dataType)) {
        writeComplexRow(row, styleBuilder, workbook);
      } else {
        throw new RuntimeError(ErrorType.IllegalArgument, "Unknown data type: " + dataType);
      }
      currentRow = nextRow();
    }
  }

  private void writeComplexRow(XLSXRowTemplate row, ExportStyleBuilder styleBuilder, Workbook workbook) {
    List<XLSXCellTemplate> cells = row.getCells();
    int fromRowNum = currentRow.getRowNum();
    if(row.getRowHeight() > 0) {
      currentRow.setHeightInPoints(row.getRowHeight() * sheetWriter.getSheet().getDefaultRowHeightInPoints());
    }
    int toRowNum = row.getRowspan() + fromRowNum;
    for (int i = fromRowNum; i < toRowNum; i++) currentRow = nextRow();

    for (XLSXCellTemplate cell : cells) {
      try {
        CellStyle cellStyle = styleBuilder.build(cell.getStyle());
        if(cell.isMerged()) {
          int startColNum = cell.getColStart();
          if(cell.getRowStart() == 0) cell.setRowStart(fromRowNum);
          if(cell.getColEnd() == 0) cell.setColEnd(cell.getColStart());
          else if(cell.getColEnd() < cell.getColStart()) {
            cell.setColEnd(cell.getColStart() + cell.getColEnd());
          }
          Row startRow = sheetWriter.getSheet().getRow(cell.getRowStart());
          XLSXExportHelper.mergedRegionBorder(sheetWriter, cell.toCellRangeAddress());
          writeCell(workbook, startRow, cell, startColNum, cellStyle);
        } else {
          writeCell(workbook, currentRow, cell, cell.getColStart(), cellStyle);
        }
      } catch(Exception e) {
        DataSerializer.JSON.dump(cell);
        throw new RuntimeError(ErrorType.IllegalArgument, "Failed to write cell: ", e);
      }
    }
  }

  private void writePrimitiveRow(XLSXRowTemplate row, ExportStyleBuilder styleBuilder, Workbook workbook) {
    if(row.getRowHeight() > 0) {
      currentRow.setHeightInPoints(row.getRowHeight() * sheetWriter.getSheet().getDefaultRowHeightInPoints());
    }
    List<XLSXCellTemplate> cells = row.getCells();
    for (XLSXCellTemplate cell : cells) {
      CellStyle cellStyle = styleBuilder.build(cell.getStyle());
      writeCell(workbook, currentRow, cell, cell.getColStart(), cellStyle);
    }
  }

  public void writeRecords(ExportStyleBuilder styleBuilder, Map<Integer, SelectField> selectFieldMap, List<MapObject> records) {
    for (MapObject record : records) {
      final MapObject __style__ = record.getMapObject("_style_");
      CellStyle cellStyle = styleBuilder.build(StyleConfig.ROW());
      if (Objects.nonNull(__style__)) {
        String style = DataSerializer.JSON.toString(__style__);
        StyleConfig styleConfig = DataSerializer.JSON.fromString(style, StyleConfig.class);
        cellStyle = styleBuilder.build(styleConfig);
      }

      Workbook workbook = sheetWriter.getSheet().getWorkbook();
      for (int i = 0; i < selectFieldMap.values().size(); i++) {
        SelectField field = selectFieldMap.get(i);
        writeCell(workbook, field, record, i, cellStyle);
      }
      nextRow();
    }

  }

  public void writeCell(Workbook workbook, Row row, XLSXCellTemplate cell, int colIdx, CellStyle cellStyle) {
    String val = cell.getValue();
    Cell cellData = row.createCell(colIdx);
    String dataType = cell.getDataType() == null ? "" : cell.getDataType().toLowerCase();
    try {
      switch (dataType) {
        case "date" -> {
          val = DateUtil.asCompactDate(DateUtil.parseCompactDate(val));
          cellData.setCellValue(val);
        }
        case "currency", "double", "float", "number", "integer" -> {
          double value = Double.parseDouble(val);
          DataFormat format = workbook.createDataFormat();
          if(value == Math.floor(value)) {
            cellData.setCellValue(value);
            cellStyle.setDataFormat(format.getFormat("#,##0"));
          } else {
            cellData.setCellValue(value);
            cellStyle.setDataFormat(format.getFormat("#,##0.00"));
          }
        } default -> {
          cellData.setCellValue(val);
        }
      }
    }  catch (Exception e) {
      log.error("Type casting error for field '{}'. Using fallback string value.", val, e);
      val = "";
      cellData.setCellValue(val);
    }
    cellData.setCellStyle(cellStyle);
  }

  public void writeCell(Workbook workbook, SelectField field, MapObject record, int colIdx, CellStyle cellStyle) {
    final String name = field.getName();
    Cell cellData = currentRow.createCell(colIdx);
    String val = "";
    String dataType = field.getDataType() == null ? "" : field.getDataType().toLowerCase();
    try {
      switch (dataType) {
        case "date" -> {
          Date date = record.getDate(name, null);
          if (date != null) val = DateUtil.asCompactDate(date);
          cellData.setCellValue(val);
        }
        case "currency", "double", "float", "number", "integer" -> {
          Double price = record.getDouble(name, 0D);
          if (price == 0) {
//            cellData.setCellValue("-");
            cellData.setCellValue(0D);
          } else {
            DataFormat format = workbook.createDataFormat();
            if (price == Math.floor(price)) {
              cellData.setCellValue(price);
              cellStyle.setDataFormat(format.getFormat("#,##0"));
            } else {
              cellData.setCellValue(price);
              cellStyle.setDataFormat(format.getFormat("#,##0.00"));
            }
          }
        }
        default -> {
          val = record.getString(name, "");
          cellData.setCellValue(val);
        }
      }
    } catch (Exception e) {
      log.error("Type casting error for field '{}'. Using fallback string value.", name, e);
      // Fallback to string value
      val = record.getString(name, "");
      cellData.setCellValue(val);
    }
    cellData.setCellStyle(cellStyle);
  }

}