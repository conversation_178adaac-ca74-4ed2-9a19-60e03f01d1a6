package cloud.datatp.tms.document.repository;

import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import cloud.datatp.tms.document.entity.InvoiceSummaryReconcile;

import java.util.List;

@Repository
public interface InvoiceSummaryReconcileRepository extends JpaRepository<InvoiceSummaryReconcile, Long>{
	@Query("SELECT ir FROM InvoiceSummaryReconcile ir WHERE companyId = :companyId AND id = :id")
	InvoiceSummaryReconcile loadById(@Param("companyId") Long companyId, @Param("id") Long id);

	@Modifying
	@Query("UPDATE InvoiceSummaryReconcile ir SET ir.storageState = :state WHERE ir.id IN (:ids)")
	int setStorageState(@Param("state") StorageState state, @Param("ids") List<Long> ids);

	@Modifying
	@Query("UPDATE InvoiceSummaryReconcile ir SET ir.messageId = :messageId WHERE ir.companyId = :companyId AND ir.id = :loloId")
  int updateLOLOMessageId(@Param("companyId") Long companyId, @Param("loloId") Long loloId, @Param("messageId") Long messageId);

	@Modifying
	@Query("UPDATE InvoiceSummaryReconcile ir SET ir.responsibleAccountId = :responsibleAccountId , ir.responsibleFullName = :responsibleFullName WHERE ir.companyId = :companyId AND ir.id = :id")
	int updatePIC(@Param("companyId") Long companyId, @Param("id") Long id, @Param("responsibleAccountId") Long responsibleAccountId,  @Param("responsibleFullName") String responsibleFullName);
}
