package cloud.datatp.fleet.vehicle;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.fleet.vehicle.entity.Transporter;
import cloud.datatp.fleet.vehicle.entity.TransporterMembership;
import cloud.datatp.fleet.vehicle.entity.Vehicle;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetCoordinator;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetMembership;
import lombok.Getter;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;

@Service("VehicleFleetService")
public class VehicleFleetService {
  @Autowired @Getter
  private VehicleFleetLogic vehicleFleetLogic;
  
  @Autowired @Getter
  private TransporterLogic transporterLogic;

  @Autowired @Getter
  private VehicleLogic vehicleLogic;

  //Transport Fleet
  @Transactional(readOnly = true)
  public VehicleFleet getVehicleFleet(ClientInfo client, Company company, String code) {
    return vehicleFleetLogic.getVehicleFleet(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public VehicleFleet getVehicleFleetById(ClientInfo client, Company company, Long id) {
    return vehicleFleetLogic.getVehicleFleetById(client, company, id);
  }

  @Transactional(readOnly = true)
  public VehicleFleet getVehicleFleetByOwnerAccountId(ClientInfo client, Company company, Long ownerAccountId) {
    return vehicleFleetLogic.getVehicleFleetByOwnerAccountId(client, company, ownerAccountId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleFleets(ClientInfo clientInfo, Company company, SqlQueryParams params) {
    return vehicleFleetLogic.searchVehicleFleets(clientInfo, company, params);
  }

  @Transactional
  public boolean changeVehicleFleetStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return vehicleFleetLogic.changeVehicleFleetStorageState(client, req);
  }
  
  @Transactional
  public VehicleFleet saveVehicleFleet(ClientInfo client, Company company, VehicleFleet vFleet) {
    return vehicleFleetLogic.saveVehicleFleet(client, company, vFleet);
  }

  @Transactional
  public List<Vehicle> processVehicles(ClientInfo client, Company company, VehicleFleet fleet, List<Vehicle> vehicles) {
    return vehicleLogic.processVehicles(client, company, fleet, vehicles);
  }


  //Transport Membership
  @Transactional
  public VehicleFleetMembership addVehicleFleetMembership(ClientInfo client, Company company, VehicleFleetMembership membership) {
    return vehicleFleetLogic.addVehicleFleetMembership(client, company, membership);
  }

  @Transactional
  public List<VehicleFleetMembership> addVehicleFleetMembershipList(ClientInfo client, Company company, List<VehicleFleetMembership> memberships) {
    return vehicleFleetLogic.addVehicleFleetMembershipList(client, company, memberships);
  }

  @Transactional
  public boolean removeVehicleFleetMemberships(ClientInfo client, Company company, List<VehicleFleetMembership> memberships) {
    return vehicleFleetLogic.removeVehicleFleetMemberships(client, company, memberships);
  }
  
  // Transport Fleet Coordinator
  @Transactional(readOnly = true)
  public VehicleFleetCoordinator getVehicleFleetCoordinator(ClientInfo client, Company company, String code) {
    return vehicleFleetLogic.getVehicleFleetCoordinator(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public VehicleFleetCoordinator getVehicleFleetCoordinatorById(ClientInfo client, Company company, Long id) {
    return vehicleFleetLogic.getVehicleFleetCoordinatorById(client, company, id);
  }
  
  @Transactional(readOnly = true)
  public VehicleFleetCoordinator getVehicleFleetCoordinatorByLoginId(ClientInfo client, Company company, String fleetCode, String loginId) {
    return vehicleFleetLogic.getVehicleFleetCoordinatorByLoginId(client, company, fleetCode, loginId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchVehicleFleetCoordinators(ClientInfo clientInfo, Company company, SqlQueryParams params) {
    return vehicleFleetLogic.searchVehicleFleetCoordinators(clientInfo, company, params);
  }

  @Transactional
  public boolean changeVehicleFleetCoordinatorsStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return vehicleFleetLogic.changeVehicleFleetCoordinatorsStorageState(client, req);
  }
  
  @Transactional
  public VehicleFleetCoordinator saveVehicleFleetCoordinator(ClientInfo client, Company company, VehicleFleetCoordinator coordinator) {
    return vehicleFleetLogic.saveCoordinator(client, company, coordinator);
  }
  
  // Transporter
  @Transactional(readOnly = true)
  public Transporter loadTransporterByCode(ClientInfo client, Company company, String code) {
    return transporterLogic.loadTransporterByCode(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public Transporter loadTransporterById(ClientInfo client, Company company, Long id) {
    return transporterLogic.loadTransporterById(client, company, id);
  }
  
  @Transactional
  public Transporter saveTransporter(ClientInfo client, Company company, Transporter transporter) {
    return transporterLogic.saveTransporter(client, company, transporter);
  }

  @Transactional
  public List<Transporter> processTransporters(ClientInfo client, Company company, VehicleFleet fleet, List<Transporter> transporters) {
    return transporterLogic.processTransporters(client, company, fleet, transporters);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchTransporters(ClientInfo client, Company company, SqlQueryParams params) {
    return transporterLogic.searchTransporters(client, company, params);
  }

  @Transactional
  public Boolean changeTransportersStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    return transporterLogic.changeTransportersStorageState(client, req);
  }

  @Transactional
  public Boolean deleteTransporters(ClientInfo client, Company company, List<Long> ids) {
    return transporterLogic.deleteTransporters(client, company, ids);
  }

  @Transactional
  public boolean removeTransporterMemberships(ClientInfo client, Company company, List<TransporterMembership> memberships) {
    return transporterLogic.removeTransporterMemberships(client, company, memberships);
  }

  @Transactional
  public TransporterMembership addTransporterMembership(ClientInfo client, Company company, TransporterMembership membership) {
    return transporterLogic.addTransporterMembership(client, company, membership);
  }

  @Transactional
  public List<TransporterMembership> addTransporterMembershipList(ClientInfo client, Company company, List<TransporterMembership> memberships) {
    return transporterLogic.addTransporterMembershipList(client, company, memberships);
  }
}