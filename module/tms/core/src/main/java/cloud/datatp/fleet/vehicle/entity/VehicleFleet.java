package cloud.datatp.fleet.vehicle.entity;

import java.io.Serial;
import java.util.HashSet;
import java.util.Set;

import cloud.datatp.tms.partner.entity.TMSWebhookConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.entity.CompanyEntity;

@Entity
@Table(
  name = VehicleFleet.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = VehicleFleet.TABLE_NAME + "_code",
      columnNames = {"company_id", "code"}),
    @UniqueConstraint(
      name = VehicleFleet.TABLE_NAME + "_owner_account_id",
      columnNames = {"company_id", "owner_account_id"}),
  },
  indexes = { @Index(columnList = "code")}
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class VehicleFleet extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "lgc_fleet_vehicle_fleet";
  public enum FleetType { Truck, Motorbike }
  public enum FleetResource { EXTERNAL_COMPANY, INTERNAL_COMPANY }
  
  @NotNull
  private String    code;
  
  @Column(length=50, name="bfs_one_code")
  private String bfsOneCode;
  
  private String    label;
  private String    description;
  
  @Column(name = "region_of_responsibility_code")
  private String regionOfResponsibilityCode;

  @Enumerated(EnumType.STRING)
  @Column(name="fleet_type")
  private FleetType fleetType = FleetType.Truck;
  
  @Enumerated(EnumType.STRING)
  @Column(name="fleet_resource")
  private FleetResource fleetResource = FleetResource.INTERNAL_COMPANY;
  
  @Column(name = "owner_account_id")
  private Long ownerAccountId;
  
  @Column(name = "owner_full_name")
  private String ownerFullName;
  
  //drop
  @Column(name = "owner_login_id")
  private String ownerLoginId;
  
  @Column(name = "owner_login_label")
  private String ownerLoginLabel;
  
  private Integer index;
  
  private Boolean gps;

  @Column(name = "allow_request_tracking_app")
  private Boolean allowRequestTrackingApp;

  @Embedded
  private TMSWebhookConfig webhookConfig;

  @Column(length=1024 * 2)
  @Convert(converter = StringSetConverter.class)
  private Set<String> emails = new HashSet<>();
  
  public VehicleFleet withOwner(Account owner) {
    this.ownerAccountId = owner.getId();
    this.ownerFullName  = owner.getFullName();
    return this;
  }
}