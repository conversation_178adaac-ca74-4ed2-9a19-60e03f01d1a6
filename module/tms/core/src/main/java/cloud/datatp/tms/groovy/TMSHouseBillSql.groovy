package cloud.datatp.tms.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.core.security.entity.DataScope
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.StringUtil

public class TMSHouseBillSql extends Executor {
  static public class SearchTMSBill extends ExecutableSqlBuilder {
    private String JOIN_PERMISSION_FILTER(MapObject sqlParams) {
      if(!sqlParams.has("withCustomerPermission")) return "";
      String scopeStr = sqlParams.getString("dataScope", DataScope.Company.toString())
      DataScope scope = DataScope.valueOf(scopeStr);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      return """${isOwnerScope ? "" : "--"} INNER JOIN permission_filter per_filter ON per_filter.customer_id  = bill.customer_id""";
    }
    
    private String WITH_PERMISSION_FILTER(MapObject sqlParams) {
      if(!sqlParams.has("withCustomerPermission")) return "";
      String scopeStr = sqlParams.getString("dataScope", DataScope.Company.toString())
      DataScope scope = DataScope.valueOf(scopeStr);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      if(isOwnerScope == true) {
        return """
          WITH permission_filter AS (
            SELECT
              cus.id AS customer_id
            FROM lgc_tms_partner_permission per
            INNER JOIN lgc_tms_customer cus ON cus.tms_partner_id = per.tms_partner_id
            WHERE
              ${FILTER_BY_PARAM("per.company_id", "companyId", sqlParams)}
              ${AND_FILTER_BY_PARAM("per.user_id", "responsibleAccountId", sqlParams)}
            GROUP BY cus.id
          )
        """
      }
      return "";
    }
    
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams          = ctx.getParam("sqlParams");
      String searchPattern         = sqlParams.getString("searchPattern", null);
      Boolean filterByIds          = sqlParams.has("ids");
      String excludeRoundUsed      = sqlParams.get("excludeRoundUsed", null);
      Boolean moderatorPermission  = sqlParams.getBoolean("moderatorPermission", false);
      String dataScope             = sqlParams.getString("dataScope");
      boolean isOwner              = DataScope.Owner.toString().equals(dataScope) && !sqlParams.has("withCustomerPermission");
      
      RangeFilter rangeFilter      = (RangeFilter) sqlParams.get("date_time");
      String filterDateTimeIsNull  = (rangeFilter == null || StringUtil.isBlank(rangeFilter.getToValue())) ? "OR delivery_plan IS NULL" : "";
      boolean excludeFilterByDate  = rangeFilter == null || (StringUtil.isBlank(rangeFilter.getFromValue()) && StringUtil.isBlank(rangeFilter.getToValue())) ? false : true;
      if(StringUtil.isNotBlank(searchPattern)) {
        searchPattern = "%" + searchPattern + "%";
        excludeFilterByDate = false;
      }
      
      String query = """
        ${WITH_PERMISSION_FILTER(sqlParams)}
        SELECT * FROM (
          SELECT
            hb.id                   AS hbl_id,
            hb.hbl_no,
            TO_CHAR(hb.last_shipping_date, 'yyyy/MM/dd')       AS formarted_date_time,
            hb.file_no,
            hb.purpose,
            hb.method,
            hb.type_of_transportation,
            hb.root,
            hb.customer_full_name,
            hb.customer_id,
  
            hb.warehouse_location_id,
            hb.warehouse_location_label,
            hb.carrier_id,
            hb.carrier_full_name,
            hb.eta_cut_off_time_note,

            hb.quantity                   AS hbl_quantity,
            hb.quantity_unit              AS hbl_quantity_unit,
            hb.volume                     AS hbl_volume,
            hb.volume_note                AS hbl_volume_note,
            hb.weight                     AS hbl_weight,
  
            hb.declaration_number,
            hb.booking_code,
            hb.verify_hbl_no,
            hb.created_by,
            hb.created_time,
            hb.modified_by,
            hb.modified_time,
            hb.version,
            hb.storage_state,
            hb.company_id,

            bill.quantity,
            bill.quantity_unit,
            bill.volume,
            bill.volume_as_text,
            bill.weight,
            bill.goods_description,

            bill.id,
            bill.code,
            bill.state,
            bill.process_status,
            bill.type,
            bill.tms_bill_source,
            bill.delivery_plan                              AS delivery_plan,
            bill.time,
            bill.description,
            bill.status_issue,
            bill.container_in_hand_plan_id,
            bill.estimate_time,
            bill.modified_estimate_time,
            bill.delayed_time,
            bill.responsible_account_id,
            bill.responsible_full_name,
            CASE
              WHEN account_user_profile.nickname IS NOT NULL THEN account_user_profile.nickname
              ELSE account_user_profile.full_name
            END AS user_name,
      
            bill.sender_full_name,
            bill.sender_contact,
            bill.sender_address,
            bill.sender_street_name,
            bill.sender_location_id      AS sender_location_id,
            sender_location.address              AS sender_location_address,
            sender_location.subdistrict_label    AS sender_location_subdistrict,
            sender_location.state_label          AS sender_location_state,
            sender_location.short_label          AS sender_location_short_label,
      
            bill.receiver_full_name,
            bill.receiver_contact,
            bill.receiver_address,
            bill.receiver_street_name,
            bill.receiver_location_id,
            receiver_location.subdistrict_label  AS receiver_location_subdistrict,
            receiver_location.state_label        AS receiver_location_state,
            receiver_location.address            AS receiver_location_address,
            receiver_location.short_label        AS receiver_location_short_label,
      
            bill.job_tracking_id,
            CASE
                 WHEN bill.accounting_report_date IS NOT NULL THEN 'paid'
                 ELSE                                                      'notPaid'
            END                                         AS paid,
      
            lgc_tms_bill_forwarder_transport.mode,
            lgc_tms_bill_forwarder_transport.truck_type,
            lgc_tms_bill_forwarder_transport.container_no,
            lgc_tms_bill_forwarder_transport.seal_no,
            lgc_tms_bill_forwarder_transport.warehouse_label,
            lgc_tms_bill_forwarder_transport.vendor_full_name,
            lgc_tms_bill_forwarder_transport.vendor_id,
            vehicle_fleet.bfs_one_code                          as vendor_bfs_one_code,
            lgc_tms_bill_forwarder_transport.truck_no,
            CASE
              WHEN process_status = 'PLAN' THEN 1
              WHEN process_status = 'PENDING' THEN 2
              WHEN process_status = 'PROCESSING' THEN 3
              ELSE 4
            END AS status_number,
            CASE
              WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_FCL' THEN 1
              WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_LCL' THEN 2
              WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_AIR' THEN 3
              WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_FCL' THEN 4
              WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_LCL' THEN 5
              WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_AIR' THEN 6
              ELSE 7
            END AS mode_number,
            
            lgc_tms_bill_fee.id AS bill_fee_id,
            lgc_tms_bill_fee.fixed_payment,
            lgc_tms_bill_fee.extra_payment,
            lgc_tms_bill_fee.total_payment,
            lgc_tms_bill_fee.final_payment,
            lgc_tms_bill_fee.payment_note,
            lgc_tms_bill_fee.pay_on_behalf_customer_id,
            lgc_tms_bill_fee.pay_on_behalf_customer_name,
            lgc_tms_bill_fee.payment_status,
      
            lgc_job_tracking.job_tracking_project_id,
            lgc_job_tracking.step_done_count,
            lgc_job_tracking.last_step_name as current_step,
            
            --Vendor fields
            lgc_tms_vendor_bill.id                      AS vendor_bill_id,
            lgc_tms_vendor_bill.fixed                   AS vendor_fixed,
            lgc_tms_vendor_bill.extra                   AS vendor_extra,
            lgc_tms_vendor_bill.cost                    AS vendor_cost,
            lgc_tms_vendor_bill.send_vendor             AS vendor_bill_send_vendor,
   
            -- operations fields
            lgc_tms_operations.status                   AS ops_status,
            lgc_tms_operations.id                       AS ops_id,
            lgc_tms_operations.note                     AS ops_note,
            lgc_tms_operations.ops_account_full_name    AS ops_account_full_name,
            lgc_tms_operations.ops_account_mobile       AS ops_account_mobile,
            lgc_tms_operations.identification_no        AS ops_identification_no,
      
            -- RU fields
            lgc_tms_round_used.id AS round_used_id,
            lgc_tms_round_used.status AS round_used_status,
            lgc_tms_vendor_bill.vendor_cost_status      AS vendor_cost_status,

            --Message fields,
            mm.status                                   AS message_status,
            mm.id                                       AS message_id

          FROM lgc_tms_house_bill hb
          INNER JOIN lgc_tms_bill bill
            ON hb.id                                        = bill.tms_house_bill_id
          INNER JOIN lgc_tms_bill_forwarder_transport
           ON lgc_tms_bill_forwarder_transport.id           = bill.tms_bill_forwarder_transport_id
          LEFT JOIN lgc_tms_bill_fee
           ON lgc_tms_bill_fee.id                           = bill.tms_bill_fee_id
          LEFT JOIN lgc_job_tracking
            ON lgc_job_tracking.id                          = bill.job_tracking_id
                 
          LEFT JOIN lgc_tms_operations
            ON lgc_tms_operations.tms_bill_id               = bill.id
          LEFT JOIN lgc_tms_round_used
            ON lgc_tms_round_used.tms_bill_id               = bill.id
          LEFT JOIN lgc_tms_vendor_bill
            ON lgc_tms_vendor_bill.tms_bill_id              = bill.id

          LEFT JOIN account_account
            ON bill.responsible_account_id                  = account_account.id
          LEFT JOIN account_user_profile
            ON account_user_profile.login_id                = account_account.login_id

          LEFT JOIN lgc_fleet_vehicle_fleet vehicle_fleet
            ON vehicle_fleet.id                             = lgc_tms_bill_forwarder_transport.vendor_id
      
          LEFT JOIN settings_location sender_location
            ON sender_location.id                           = bill.sender_location_id
          LEFT JOIN settings_location receiver_location
            ON receiver_location.id                         = bill.receiver_location_id

          LEFT JOIN message_message mm 
            ON mm.id                                        = bill.message_id

          ${JOIN_PERMISSION_FILTER(sqlParams)}
        ) as lgc_tms_bill
        WHERE
          ${FILTER_BY_STORAGE_STATE(sqlParams)}
          ${AND_FILTER_BY_OPTION("paid", "paid", sqlParams)}
          --${AND_FILTER_BY_OPTION("plan_status", "plan", sqlParams)}
          ${AND_FILTER_BY_PARAM("companyId", sqlParams)}
          ${AND_FILTER_BY_PARAM("customerId", sqlParams)}
          ${AND_FILTER_BY_PARAM("carrierId", sqlParams)}
          ${AND_FILTER_BY_PARAM("hblId", sqlParams)}
          ${AND_SEARCH_BY(["file_no", "responsible_full_name", "customer_full_name"], searchPattern)}
          ${isOwner ? AND_FILTER_BY_PARAM("lgc_tms_bill.responsible_account_id", "responsibleAccountId", sqlParams) : ""}
          ${excludeFilterByDate ? "" : "--"} AND (${FILTER_BY_RANGE('lgc_tms_bill.delivery_plan', 'date_time', sqlParams)} ${filterDateTimeIsNull})
          ${filterByIds ? """ AND id IN ${sqlParams.getString("ids").replace("[", "(").replace("]", ")")}""" : ""}
          ${excludeRoundUsed ? " AND round_used_id IS NULL" : ""}
          ORDER BY formarted_date_time DESC, hbl_id, customer_full_name, booking_code, file_no, mode_number, time
          ${MAX_RETURN(sqlParams)};
      """;
      return query;
    }
  }
  
  public TMSHouseBillSql() {
    register(new SearchTMSBill())
  }
}
