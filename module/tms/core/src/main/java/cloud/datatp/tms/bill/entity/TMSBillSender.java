package cloud.datatp.tms.bill.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Embeddable
@Getter @Setter @NoArgsConstructor
public class TMSBillSender {

  @Column(name = "sender_full_name")
  private String senderFullName;

  @Column(name = "sender_contact")
  private String senderContact;

  @Column(name = "sender_address", length = 16*1024)
  private String senderAddress;
  
  @Column(name = "sender_map_lat")
  private Double senderLat;
  
  @Column(name = "sender_map_lng")
  private Double senderLng;
  
  @Column(name = "sender_inv_address")
  private String senderInvAddress;
  
  @Column(name = "sender_location_id")
  private Long senderLocationId;
  
  public String getSenderMobile() {
    return senderContact;
  }
}