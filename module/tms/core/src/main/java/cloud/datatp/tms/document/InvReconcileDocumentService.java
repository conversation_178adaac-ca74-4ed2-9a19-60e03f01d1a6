package cloud.datatp.tms.document;

import java.util.List;

import net.datatp.module.communication.entity.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.tms.document.entity.InvoiceSummaryReconcile;
import cloud.datatp.tms.document.entity.InvoiceReconcileDocument;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;

@Service("InvReconcileDocumentService")
public class InvReconcileDocumentService extends DAOService {
  @Autowired
  private InvReconcileDocumentLogic logic;

  @Transactional(readOnly = true)
  public InvoiceSummaryReconcile loadInvoiceSummaryReconcileById(ClientInfo clientInfo, Company company, Long id) {
    return logic.loadInvoiceSummaryReconcileById(clientInfo, company, id);
  }

  @Transactional
  public InvoiceSummaryReconcile saveTMSLOLOInvoiceReconcile(ClientInfo clientInfo, Company company, InvoiceSummaryReconcile lolo) {
    return logic.saveInvoiceSummaryReconcile(clientInfo, company, lolo);
  }

  @Transactional
  public List<InvoiceSummaryReconcile> saveInvoiceSummaryReconciles(ClientInfo clientInfo, Company company, List<InvoiceSummaryReconcile> lolos) {
    return logic.saveInvoiceSummaryReconciles(clientInfo, company, lolos);
  }

  @Transactional
  public boolean updatePICInvoiceSummaryReconciles(ClientInfo clientInfo, Company company, Long id) {
    return logic.updatePICInvoiceSummaryReconciles(clientInfo, company, id);
  }



  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchInvoiceSummaryReconciles(ClientInfo client , Company company, SqlQueryParams sqlParams) {
    return logic.searchInvoiceSummaryReconciles(client, company, sqlParams);
  }

  @Transactional
  public boolean changeInvoiceSummaryReconcileStorageState(ClientInfo client, Company company, ChangeStorageStateRequest req) {
    logic.changeInvoiceSummaryReconcileStorageState(client, company, req);
    return true;
  }

  @Transactional
  public boolean deleteInvoiceSummaryReconciles(ClientInfo client, Company company, List<Long> ids) {
    logic.deleteInvoiceSummaryReconciles(client, company, ids);
    return true;
  }

  @Transactional
  public boolean deleteInvoiceReconcileDocument(ClientInfo client, Company company, Long docSetId, Long loloId) {
    logic.deleteInvoiceReconcileDocument(client, company, docSetId, loloId);
    return true;
  }
  
  @Transactional
  public InvoiceReconcileDocument createInvoiceReconcileDocument(ClientInfo client, Company company, Long docSetId, Long loloId) {
    return logic.createInvoiceReconcileDocument(client, company, docSetId, loloId);
  }

  @Transactional
  public Message createInvoiceReconcileMessage(ClientInfo client, Company company, Long loloId) {
    return logic.createInvoiceReconcileMessage(client, company, loloId);
  }

  @Transactional
  public boolean updateInvoiceReconcileMessageId(ClientInfo client, Company company, Long loloId, Long messageId) {
    return logic.updateInvoiceReconcileMessageId(client, company, loloId, messageId);
  }
}