package cloud.datatp.fleet.vehicle.repository;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import net.datatp.module.data.db.entity.StorageState;

public interface VehicleFleetRepository extends JpaRepository<VehicleFleet, Serializable> {
  @Query( "SELECT tf from VehicleFleet tf WHERE tf.companyId = :companyId AND tf.code = :code" )
  VehicleFleet getByCode(@Param("companyId") Long companyId, @Param("code") String code);

  @Query( "SELECT tf from VehicleFleet tf WHERE tf.companyId = :companyId AND tf.bfsOneCode = :bfsOneCode" )
  List<VehicleFleet> findByBfsOneCode(@Param("companyId") Long companyId, @Param("bfsOneCode") String bfsOneCode);
  
  @Query( "SELECT tf from VehicleFleet tf WHERE tf.companyId = :companyId AND tf.id = :id" )
  VehicleFleet getById(@Param("companyId") Long companyId, @Param("id") Long id);

  @Query( "SELECT tf from VehicleFleet tf WHERE tf.companyId = :companyId AND tf.ownerAccountId = :ownerAccountId" )
  VehicleFleet getByOwnerAccountId(@Param("companyId") Long companyId, @Param("ownerAccountId") Long ownerAccountId);
  
  @Query( "SELECT tf from VehicleFleet tf WHERE tf.companyId = :companyId" )
  List<VehicleFleet> findByCompanyId(@Param("companyId") Long companyId);
  
  @Modifying
  @Query("update VehicleFleet tf SET tf.storageState = :storageState WHERE tf.id IN :ids")
  int setStorageState(@Param("storageState") StorageState state, @Param("ids") List<Long> ids);
}