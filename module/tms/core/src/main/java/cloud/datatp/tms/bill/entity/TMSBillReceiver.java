package cloud.datatp.tms.bill.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Embeddable
@Getter @Setter @NoArgsConstructor
public class TMSBillReceiver {
  @Column(name = "receiver_full_name")
  private String receiverFullName;

  @Column(name = "receiver_contact")
  private String receiverContact;

  @Column(name = "receiver_address", length = 16*1024)
  private String receiverAddress;
  
  @Column(name = "receiver_map_lat")
  private Double receiverLat;
  
  @Column(name = "receiver_map_lng")
  private Double receiverLng;
  
  @Column(name = "receiver_inv_address")
  private String receiverInvAddress;
  
//  @Column(name = "receiver_map_address", length = 16*1024)
//  private String receiverMapAddress;
  
  @Column(name = "receiver_location_id")
  private Long receiverLocationId;
 
  public String getReceiverMobile() {
    return receiverContact;
  }
}