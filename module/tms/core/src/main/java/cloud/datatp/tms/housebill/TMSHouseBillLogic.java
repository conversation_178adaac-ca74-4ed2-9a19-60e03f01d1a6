package cloud.datatp.tms.housebill;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.bfsone.BFSOneDataLogic;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillGoods;
import cloud.datatp.tms.bill.entity.TMSBillType;
import cloud.datatp.tms.housebill.entity.TMSHouseBill;
import cloud.datatp.tms.housebill.entity.TMSHouseBillGoods;
import cloud.datatp.tms.housebill.entity.TMSImportAndExport;
import cloud.datatp.tms.housebill.repository.TMSHouseBillRepository;
import cloud.datatp.tms.partner.TMSCustomerLogic;
import cloud.datatp.tms.partner.TMSPartnerLogic;
import cloud.datatp.tms.partner.entity.TMSCustomer;
import cloud.datatp.tms.partner.entity.TMSPartner;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.app.AppEnv;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;

@Component
public class TMSHouseBillLogic extends DAOService {
  @Autowired
  protected TMSHouseBillRepository repo;
  
  @Autowired
  protected TMSBillLogic tmsBillLogic;
  
  @Autowired
  protected AccountLogic accountLogic;
  
  @Autowired
  protected TMSCustomerLogic customerLogic;
  
  @Autowired
  protected TMSPartnerLogic  partnerLogic;
  
  @Autowired
  protected BFSOneDataLogic bfsOneDataLogic;
  
  @Autowired
  protected AppEnv appEnv;
  
  @Autowired
  protected ExecutableUnitManager executableUnitManager;
  
  public TMSHouseBill getById(ClientInfo client, Company company, Long id) {
    return repo.getById(company.getId(), id);
  }

  public boolean deleteByIds(ClientInfo client, Company company, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }
  
  private void onPreSave(ClientInfo client, Company company, TMSHouseBill hbl) {
    if(hbl.isNew()) return;
    
    List<TMSBill> bills = tmsBillLogic.findByHouseBill(client, company, hbl.getId());
    if(Collections.isEmpty(bills)) return;
    
    TMSHouseBill db = getById(client, company, hbl.getId());
    TMSImportAndExport importAndExportDb = db.getImportAndExport();
    TMSImportAndExport importAndExport   = hbl.getImportAndExport();
    if(importAndExportDb == null) importAndExportDb = new TMSImportAndExport();
    if(importAndExport   == null) importAndExport   = new TMSImportAndExport();
    double quantity     = 0;
    String quantityUnit = "PKG";
    double weight     = 0;
    String weightUnit = "KGM";
    double volume     = 0;
    String volumeUnit = "CBM";
    String volumeNote = "";
    for(TMSBill bill : bills) {
      if(importAndExport.getWarehouseLocationId() != importAndExportDb.getWarehouseLocationId()) {
        if(hbl.isImport()) {
          bill.getSender().setSenderContact(importAndExport.getWarehouseContact());
          bill.getSender().setSenderAddress(importAndExport.getWarehouseLocationLabel());
          bill.getSender().setSenderLocationId(importAndExport.getWarehouseLocationId());
        }
        if(hbl.isExport()) {
          bill.getReceiver().setReceiverContact(importAndExport.getWarehouseContact());
          bill.getReceiver().setReceiverAddress(importAndExport.getWarehouseLocationLabel());
          bill.getReceiver().setReceiverLocationId(importAndExport.getWarehouseLocationId());
        }
      }
      
      TMSBillGoods goods = bill.getTmsBillGoods();
      quantity     += goods.getQuantity();
      quantityUnit  = goods.getQuantityUnit();
      weight       += goods.getWeight();
      weightUnit    = goods.getWeightUnit();
      volume       += goods.getVolume();
      volumeUnit    = goods.getVolumeUnit();
      volumeNote   += goods.getVolumeAsText() != null ? goods.getVolumeAsText() : "";
      
      bill = hbl.updateTMSBill(bill);
      bill.set(client, company);
    }
    tmsBillLogic.getTmsBillRepo().saveAll(bills);
    
    TMSHouseBillGoods hbGoods = hbl.getHouseBillGoods();
    hbGoods.setQuantity(quantity);
    hbGoods.setQuantityUnit(quantityUnit);
    hbGoods.setWeight(weight);
    hbGoods.setWeightUnit(weightUnit);
    hbGoods.setVolume(volume);
    hbGoods.setVolumeUnit(volumeUnit);
    hbGoods.setVolumeNote(volumeNote);
    List<TMSBill> filters = bills.stream().filter(sel -> sel.getDeliveryPlan() != null).toList();
    if(Collections.isNotEmpty(filters)) {
      TMSBill bill = java.util.Collections.max(filters, (a, b) -> a.getDeliveryTime().compareTo(b.getDeliveryTime()));
      hbl.setLastShippingDate(bill.getDeliveryTime());
    } else {
      hbl.setLastShippingDate(null);
    }
  }
  
  public TMSHouseBill saveTMSHouseBill(ClientInfo client, Company company, TMSHouseBill hbl) {
  hbl.set(client, company);
  onPreSave(client, company, hbl);
  return repo.save(hbl);
  }

  public List<SqlMapRecord> searchTMSBills(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
        new ExecutableContext()
            .withScriptEnv(scriptDir, "cloud/datatp/tms/housebill/SearchLogicUnit.java", "SearchTMSBill")
            .withParam(this).withParam(client).withParam(company).withParam(params);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }
  
  public List<TMSHouseBill> convertTMSBillToTMSHouseBill(ClientInfo client, Company company, Long [] tmsBillIds) {
    return null;
  }
  
  public MapObject createTMSHouseBillWithBFSOneData(ClientInfo client, Company company, String hblNo) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("hblNo", hblNo);
    List<SqlMapRecord> bfsOneRecs = bfsOneDataLogic.searchBFSOneTransactionsData(client, company, params);
    if(Collections.isEmpty(bfsOneRecs)) return null;
    
    TMSHouseBill houseBill = new TMSHouseBill(bfsOneRecs.get(0));
    String shipperId = bfsOneRecs.get(0).getString("shipperId");
    
    try {
      TMSPartner partner = partnerLogic.getByBfsOneCode(client, company, shipperId);
      if(partner != null) {
        TMSCustomer customer = customerLogic.loadTMSCustomerByPartnerId(client, company, partner.getId());
        if(customer != null) {
          houseBill.setCustomerId(customer.getId());
          houseBill.setCustomerFullName(partner.getShortName());
        } else {
          houseBill.setError(shipperId + " Customer not found!");
        }
      } else {
        houseBill.setError(shipperId + " Partner not found!");
      }
    } catch (Exception e) {
      houseBill.setError(e.getMessage());
    }
    
    List<MapObject> billModels = new ArrayList<>();
    double quantity     = 0;
    String quantityUnit = "PKG";
    double weight     = 0;
    String weightUnit = "KGM";
    double volume     = 0;
    String volumeUnit = "CBM";
    String volumeNote = "";
    for(SqlMapRecord bfsRec : bfsOneRecs) {
      TMSBill bill = new TMSBill(TMSBillType.FORWARDER);
      bill.convertBFSOneData(bfsRec);
      TMSBillGoods goods = bill.getTmsBillGoods();
      quantity     += goods.getQuantity();
      quantityUnit  = goods.getQuantityUnit();
      weight       += goods.getWeight();
      weightUnit    = goods.getWeightUnit();
      volume       += goods.getVolume();
      volumeUnit    = goods.getVolumeUnit();
      volumeNote   += goods.getVolumeAsText() != null ? goods.getVolumeAsText() : "";
      bill = houseBill.updateTMSBill(bill);
      
      MapObject billModel = bill.toTMSBillMap();
      billModels.add(billModel);
    }
    TMSHouseBillGoods hbGoods = houseBill.getHouseBillGoods();
    hbGoods.setQuantity(quantity);
    hbGoods.setQuantityUnit(quantityUnit);
    hbGoods.setWeight(weight);
    hbGoods.setWeightUnit(weightUnit);
    hbGoods.setVolume(volume);
    hbGoods.setVolumeUnit(volumeUnit);
    hbGoods.setVolumeNote(volumeNote);
    
    MapObject result = new MapObject();
    result.add("houseBill", houseBill);
    result.add("bills", billModels);
    return result;
  }
}