package cloud.datatp.fleet.vehicle;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.entity.DriverTrip;
import cloud.datatp.fleet.vehicle.entity.Vehicle;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetCoordinator;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetMembership;
import cloud.datatp.fleet.vehicle.repository.VehicleFleetCoordinatorRepository;
import cloud.datatp.fleet.vehicle.repository.VehicleFleetMembershipRepository;
import cloud.datatp.fleet.vehicle.repository.VehicleFleetRepository;
import lombok.Getter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.ConditionFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.util.ds.Collections;

@Component
public class VehicleFleetLogic extends DAOService {
  @Autowired @Getter
  private VehicleFleetRepository vehicleFleetRepo;

  @Autowired
  private VehicleFleetMembershipRepository fleetMembershipRepo;

  @Autowired
  private VehicleFleetCoordinatorRepository vehicleFleetCoordinatorRepo;
  
  @Autowired
  protected ApplicationContext context;
  
  public VehicleFleet getVehicleFleet(ClientInfo client, Company company, String code) {
    return vehicleFleetRepo.getByCode(company.getId(), code);
  }
  
  public VehicleFleet getVehicleFleetById(ClientInfo client, Company company, Long id) {
    return vehicleFleetRepo.getById(company.getId(), id);
  }

  public VehicleFleet getVehicleFleetByOwnerAccountId(ClientInfo client, Company company, Long ownerAccountId) {
    return vehicleFleetRepo.getByOwnerAccountId(company.getId(), ownerAccountId);
  }
  
  public List<SqlMapRecord> searchVehicleFleets(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleSql.groovy";

    params.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchVehicleFleet", params);
  }

  public boolean changeVehicleFleetStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    vehicleFleetRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public VehicleFleet saveVehicleFleet(ClientInfo client, Company company, VehicleFleet fleet) {
    fleet.set(client, company);
    return vehicleFleetRepo.save(fleet);
  }

  public VehicleFleetMembership addVehicleFleetMembership(ClientInfo client, Company company, VehicleFleetMembership membership) {
    membership.set(client, company);
    return fleetMembershipRepo.save(membership);
  }
  
  public boolean removeVehcileFleetMembership(ClientInfo client, Company company, Vehicle vehicle) {
    fleetMembershipRepo.removeByVehicleFleet(company.getId(), vehicle.getCode());
    fleetMembershipRepo.flush();
    return true;
  }

  public boolean removeVehicleFleetMemberships(ClientInfo client, Company company, List<VehicleFleetMembership> memberships) {
    for (VehicleFleetMembership membership : memberships) {
      removeVehicleFleetMembership(client, company, membership);
    }
    return true;
  }
  
  public void removeVehicleFleetMembership(ClientInfo client, Company company, VehicleFleetMembership membership) {
    if (membership.getVehicleId() != null) {
      fleetMembershipRepo.deleteByVehicleId(company.getId(), membership.getVehicleId(), membership.getVehicleFleetId());
    } else if (membership.getCoordinatorId() != null) {
      fleetMembershipRepo.deleteByCoordinatorId(company.getId(), membership.getCoordinatorId(), membership.getVehicleFleetId());
    }
  }

  // Vehicle Fleet Coordinator
  public VehicleFleetCoordinator getVehicleFleetCoordinatorById(ClientInfo client, Company company, Long id) {
    return vehicleFleetCoordinatorRepo.getById(company.getId(), id);
  }
  
  public VehicleFleetCoordinator getVehicleFleetCoordinator(ClientInfo client, Company company, String code) {
    return vehicleFleetCoordinatorRepo.getByCode(company.getId(), code);
  }

  public VehicleFleetCoordinator getVehicleFleetCoordinatorByLoginId(ClientInfo client, Company company, String fleetCode, String loginId) {
    return vehicleFleetCoordinatorRepo.getByLoginId(company.getId(), fleetCode, loginId);
  }
  
  public VehicleFleetCoordinator getVehicleFleetCoordinatorByAccountId(ClientInfo client, Company company, Long accountId) {
    return vehicleFleetCoordinatorRepo.getByAccountId(company.getId(), accountId);
  }

  public List<SqlMapRecord> searchVehicleFleetCoordinators(ClientInfo client, Company company, SqlQueryParams params) {
    String[] SEARCH_FILTER = new String[] {"code", "label"};
    params.addParam("companyId", company.getId());
    SqlQuery query = new SqlQuery()
      .ADD_TABLE(new EntityTable(VehicleFleetCoordinator.class).selectAllFields())
        .JOIN(new Join("LEFT JOIN", Account.class)
          .ON("id", VehicleFleetCoordinator.class, "accountId")
          .addSelectField("fullName", "fullName")
          .addSelectField("email", "email")
          .addSelectField("mobile", "mobile"))
        .FILTER(ClauseFilter.company(VehicleFleetCoordinator.class))
        .FILTER(SearchFilter.isearch(VehicleFleetCoordinator.class, SEARCH_FILTER))
        .FILTER(
          OptionFilter.storageState(VehicleFleetCoordinator.class),
          RangeFilter.modifiedTime(VehicleFleetCoordinator.class))
        .FILTER(new ConditionFilter(VehicleFleetCoordinator.class, "fleetId", "= :fleetId").hasVariableCondition("fleetId"))
        .ORDERBY(new String[]{"label", "modifiedTime"}, "label", "DESC");
    if (params.hasParam("vehicleFleetId")) {
      query.JOIN(new Join("JOIN", VehicleFleetMembership.class)
        .ON("coordinatorId", VehicleFleetCoordinator.class, "id")
        .AND("vehicleFleetId", "=", ":vehicleFleetId")
        .addSelectField("vehicleFleetCode", "vehicleFleetCode"));
    }
    query.mergeValue(params);
    return query(client, query, params).getSqlMapRecords();
  }
  
  public boolean changeVehicleFleetCoordinatorsStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    vehicleFleetCoordinatorRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public VehicleFleetCoordinator saveCoordinator(ClientInfo client, Company company, VehicleFleetCoordinator coordinator) {
    coordinator.set(client, company);
    return vehicleFleetCoordinatorRepo.save(coordinator);
  }
  
  public List<SqlMapRecord> searchDriverTrips(ClientInfo client, Company company, SqlQueryParams params) {
    params.addParam("companyId", company.getId());
    SqlQuery query = new SqlQuery()
        .ADD_TABLE(new EntityTable(DriverTrip.class).selectAllFields())
        .FILTER(new ConditionFilter(DriverTrip.class, "code", "= :code").hasVariableCondition("code"))
        .FILTER(new ConditionFilter(DriverTrip.class, "driverLoginId", "= :driverLoginId")
            .hasVariableCondition("driverLoginId"))
        .FILTER(SearchFilter.isearch(DriverTrip.class, new String[]{"loginId", "label"}))
        .FILTER(ClauseFilter.company(DriverTrip.class))
        .FILTER(
            OptionFilter.storageState(DriverTrip.class),
            RangeFilter.modifiedTime(DriverTrip.class))
        .ORDERBY(new String[]{"loginId", "modifiedTime"}, "modifiedTime", "DESC");
    return query(client, query, params).getSqlMapRecords();
  }

  public List<VehicleFleetMembership> addVehicleFleetMembershipList(ClientInfo client, Company company, List<VehicleFleetMembership> memberships) {
    Collections.apply(memberships, membership -> membership.set(client, company));
    memberships = fleetMembershipRepo.saveAll(memberships);
    return memberships;
  }

}