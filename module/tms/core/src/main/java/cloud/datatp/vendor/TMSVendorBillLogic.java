package cloud.datatp.vendor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cloud.datatp.tms.bill.entity.*;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import cloud.datatp.gps.GPSTrackingLogic;
import cloud.datatp.gps.entity.GPSTrackingReport;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.ops.entity.TMSOperations;
import cloud.datatp.vendor.entity.TMSVendorAttachment;
import cloud.datatp.vendor.entity.TMSVendorBill;
import cloud.datatp.vendor.entity.TMSVendorBillTracking;
import cloud.datatp.vendor.models.TMSVendorBillModel;
import cloud.datatp.vendor.models.TMSVendorBillProjectModel;
import cloud.datatp.vendor.repository.TMSVendorAttachmentRepository;
import cloud.datatp.vendor.repository.TMSVendorBillRepository;
import groovy.lang.Binding;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.app.AppEnv;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.core.security.SessionData;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager.QueryContext;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.http.get.GETContent;
import net.datatp.module.http.get.GETService;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.project.ProjectLogic;
import net.datatp.module.project.ProjectSpaceLogic;
import net.datatp.module.project.entity.Project;
import net.datatp.module.project.entity.ProjectSpace;
import net.datatp.module.project.task.TaskLogic;
import net.datatp.module.project.task.entity.Task;
import net.datatp.module.project.task.entity.TaskComment;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.module.storage.StorageResource;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;

@Slf4j
@Component
public class TMSVendorBillLogic extends DAOService {

  @Autowired
  protected AppEnv appEnv;

  @Autowired @Getter
  private TMSVendorBillRepository repo;

  @Autowired
  protected AccountLogic accountLogic;
  
  @Autowired
  protected GPSTrackingLogic gpsTrackingLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private TMSBillLogic tmsBillLogic;

  @Autowired
  private ProjectSpaceLogic projectSpaceLogic;

  @Autowired
  private ProjectLogic projectogic;

  @Autowired
  private TaskLogic taskLogic;

  @Autowired
  private IStorageService storageService;

  @Autowired
  private AuthorizationCipherTool    cipherTool;

  @Autowired
  private UploadService              uploadService;

  @Autowired
  private GETService                 GETService;

  @Autowired
  private TMSVendorAttachmentRepository attachmentRepo;

  @Autowired
  ExecutableUnitManager executableUnitManager;

  private String QUERY_SCRIPT_DIR;

  @PostConstruct
  public void onInit() {
    if ("test".equals(env)) return;
    QUERY_SCRIPT_DIR = appEnv.addonPath("logistics", "groovy/lgc/tms");
  }

  //Remove
  public TMSVendorBill getVendorBill(ClientInfo client, Company company, Long id) {
    return repo.getById(id);
  }

  public TMSVendorBill getVendorBill(ClientInfo client, Company company, String code) {
    return repo.getByCode(company.getId(), code);
  }

  public TMSVendorBill getVendorBillById(ClientInfo client, Company company, Long id) {
    return repo.getById(company.getId(), id);
  }

  public TMSVendorBill getByTMSBillId(ClientInfo client, Company company, Long tmsBillId) {
    return repo.getByTMSBillId(company.getId(), tmsBillId);
  }
  
  public List<TMSVendorBill> findByTMSBillIds(ClientInfo client, Company company, List<Long> tmsBillIds) {
    return repo.findByTMSBillIds(company.getId(), tmsBillIds);
  }

  public boolean updateStatus(ClientInfo client, Company company, Long id, VehicleTripGoodsTrackingStatus status, String issue) {
    repo.updateStatusAndIssue(company.getId(),id, status, issue);
    return true;
  }

  public boolean updateTaskId(ClientInfo client, Company company, Long vendorBillId, Long taskId) {
    repo.updateTaskId(company.getId(), vendorBillId, taskId);
    return true;
  }

  public boolean updateLastTaskCommenter(ClientInfo client, Company company, TaskComment comment) {
    repo.updateLastTaskCommenter(company.getId(), comment.getTaskId(), comment.getById(), comment.getByLabel());
    return true;
  }

  public List<TMSVendorBill> saveVendorBillModels(ClientInfo client, Company company, List<TMSVendorBillModel> models) {
    List<TMSVendorBill> vendorBills = new ArrayList<>() ;
    for(TMSVendorBillModel model : models) {
      TMSVendorBill vendorBill = getVendorBill(client, company, model.getId());
      if(Objects.isNull(vendorBill)) {
        TMSBill bill = tmsBillLogic.getTMSBillById(client, company, model.getTmsBillId());
        vendorBill = new TMSVendorBill();
        vendorBill = vendorBill.updateByTMSBill(bill);
      }
      if(vendorBill == null)  throw RuntimeError.UnknownError("Vendor bill {0} is not found!!!", model.getFileNo());

      model.update(vendorBill);
      vendorBill = saveVendorBill(client, company, vendorBill);
      vendorBill.setUikey(model.getUikey());

      if(model.isUpdateBillRating() && vendorBill.getTmsBillId() != null) {
        TMSBillRating rating = tmsBillLogic.getTMSBillRatingByTMSBillId(client, company, vendorBill.getTmsBillId());
        model.updateTMSBillRating(rating);
        tmsBillLogic.saveTMSBillRating(client, company, rating);
      }
      vendorBills.add(vendorBill);
    }
    return vendorBills;
  }

  public List<TMSVendorBill> saveVendorBills(ClientInfo client, Company company, List<TMSVendorBill> vendorBills) {
    List<TMSVendorBill> update = new ArrayList<>();
    for(TMSVendorBill vendorBill :vendorBills ) {
      vendorBill = saveVendorBill(client, company, vendorBill);
      update.add(vendorBill);
    }
    return update;
  }


  public TMSVendorBill saveVendorBill(ClientInfo client, Company company, TMSVendorBill vendorBill) {
    if(StringUtil.isBlank(vendorBill.getCode())) {
      vendorBill.setCode(TokenUtil.idWithDateTime("VB"));
    }
    TMSBill tmsBill = tmsBillLogic.getTMSBillById(client, company, vendorBill.getTmsBillId());
    if (tmsBill != null && vendorBill.getCost() != 0) {
      vendorBill.setVendorCostStatus(TMSVendorBill.VendorCostStatus.NEED_CONFIRM);
      if(vendorBill.getCost() == tmsBill.getTmsBillFee().getTotalPayment()   &&
          vendorBill.getFixed() == tmsBill.getTmsBillFee().getFixedPayment() &&
          vendorBill.getExtra() == tmsBill.getTmsBillFee().getExtraPayment())   {
        vendorBill.setVendorCostStatus(TMSVendorBill.VendorCostStatus.AUTO_CONFIRM);
      }
    }
    vendorBill.set(client, company);
    return repo.save(vendorBill);
  }

  public boolean deleteVendorBills(ClientInfo client, Company company, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }

  public List<SqlMapRecord> oldSearchTMSVendorBills(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    sqlParams.addParam("responsible_account_id", account.getId());
    String scriptName = "TMSVendorBillQuery.groovy" ;
    if(java.util.Objects.equals(sqlParams.getParam("vendorBillNotMatch"), true)) {
      scriptName = "TMSVendorBillNotMatchQuery.groovy";
    }
    QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, scriptName);
    Binding binding = new Binding();
    binding.setVariable("company",   company);
    binding.setVariable("sqlparams", sqlParams);

    SqlSelectView view = queryContext.createSqlSelectView(binding, null);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  @SuppressWarnings("unchecked")
  public List<SqlMapRecord> searchTMSVendorBills(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
        new ExecutableContext()
            .withScriptEnv(scriptDir, "cloud/datatp/vendor/SearchLogicUnit.java", "SearchTMSVendorBill")
            .withParam(this).withParam(client).withParam(company).withParam(params);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  @SuppressWarnings("unchecked")
  public List<SqlMapRecord> searchTMSVendorBillNotLinkTMSBills(ClientInfo client, Company company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
        new ExecutableContext()
        .withScriptEnv(scriptDir, "cloud/datatp/vendor/SearchLogicUnit.java", "SearchTMSVendorBillNotLinkTMSBill")
        .withParam(this).withParam(client).withParam(company).withParam(params);
    return (List<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public TMSVendorBill getOrCreateVendorBillByTmsBill(ClientInfo client, Company company, Long tmsBillId) {
    TMSBill tmsBill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
    TMSVendorBill vendorBill = getByTMSBillId(client, company, tmsBillId);
    if(vendorBill != null) {
      return vendorBill;
    }

    vendorBill = new TMSVendorBill();
    vendorBill = vendorBill.updateByTMSBill(tmsBill);
    return saveVendorBill(client, company, vendorBill);
  }

  public List<TMSVendorBill> requestVendorBill(ClientInfo client, Company company, Long[] billIds) {
      List<TMSVendorBill> vendorBills = new ArrayList<>();
      List<TMSBill> tmsBills = tmsBillLogic.findTMSBills(client, company, billIds);
      for(TMSBill tmsBill : tmsBills) {
        if(tmsBill.getOriginVendorBillId() != null) continue;
        TMSVendorBill vendorBill = getByTMSBillId(client, company, tmsBill.getId());;
        if(vendorBill == null) vendorBill = new TMSVendorBill();
        vendorBill = vendorBill.updateByTMSBill(tmsBill);
        saveVendorBill(client, company, vendorBill);
        vendorBills.add(vendorBill);
      }
      return vendorBills;
  }

  public List<TMSVendorBill> updateVendorBill(ClientInfo client, Company company, Long[] ids) {
      List<TMSVendorBill> vendorBills = repo.getByIds(company.getId(), ids);
      for(TMSVendorBill vendorBill: vendorBills) {
        TMSBill tmsBill = tmsBillLogic.getTMSBillById(client, company, vendorBill.getTmsBillId());
        if(tmsBill != null) {
          vendorBill.updateByTMSBill(tmsBill);
        }
        saveVendorBill(client, company, vendorBill);
      }
      return vendorBills;
  }

  public List<TMSVendorAttachment> findTMSVendorAttachments(ClientInfo client, Company company, Long vendorBillId) {
    return attachmentRepo.findTMSVendorAttachments(vendorBillId);
  }

  public List<TMSVendorAttachment> vendorBillAttachFileByMail(ClientInfo client, Company company, Long vendorBillId, Message message) {
    if(message == null) return new ArrayList<>();
    if(Collections.isEmpty(message.getAttachments())) return new ArrayList<>();;

    List<TMSVendorAttachment> atts = new ArrayList<>();
    TMSVendorBill vendorBill = getVendorBill(client, company, vendorBillId);
    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    String storagePath = TMSVendorAttachment.getTMSVendorBillAttachmentStoragePath(vendorBillId);

    TMSVendorBill.TMSVendorAttachStatus status = TMSVendorBill.TMSVendorAttachStatus.SUCCESS;
    List<UploadResource> attResources = message.getAttachments();
    for(UploadResource attResource : attResources) {
      try {
        String      storeId     = attResource.getStoreId();
        StorageResource podResource = null;
        if("store".equals(attResource.getResourceScheme())) {
          SessionData sessionData = cipherTool.decryptSessionData(storeId);
          GETContent content      = GETService.get("store", sessionData.getData());
          byte[] contentBytes = content.getData();
          podResource = new StorageResource(attResource.getName(), contentBytes);
        } else {
          byte[] contentBytes = uploadService.load(storeId);
          podResource = new StorageResource(attResource.getName(), contentBytes);
        }
        podResource = storage.save(storagePath, podResource);
        TMSVendorAttachment att = new TMSVendorAttachment();
        att.setName(podResource.getName());
        att.setLabel(podResource.getName());
        att.setDownloadUri(podResource.getDownloadUri());
        att.setPublicDownloadUri(podResource.getPublicDownloadUri());
        att.setResourceUri(podResource.getResourceUri());
        att.setVendorBillId(vendorBillId);
        att.setSize(podResource.toBytes().length);
        att.set(client, company.getId());
        atts.add(att);
        attachmentRepo.deleteByName(company.getId(), att.getVendorBillId(), att.getName());
      } catch (Exception e) {
        status = TMSVendorBill.TMSVendorAttachStatus.FAIL;
        log.error("Vendor Attach Error", e);
      }
    }
    vendorBill.setAttachStatus(status);
    saveVendorBill(client, company, vendorBill);
    return attachmentRepo.saveAll(atts);
  }

  public List<TMSVendorAttachment> saveTMSVendorAttachments(
      ClientInfo client, Company company, Long vendorBillId, List<TMSVendorAttachment> attachments, boolean removeOrphan) {
    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    String storagePath = TMSVendorAttachment.getTMSVendorBillAttachmentStoragePath(vendorBillId);
    storage.saveAttachments(storagePath, attachments, removeOrphan);
    for (TMSVendorAttachment attachment : attachments) {
      attachment.setVendorBillId(vendorBillId);
      attachment.set(client, company.getId());
    }
    attachments = attachmentRepo.saveAll(attachments);
    if(removeOrphan) {
      List<Long> idSet = TMSVendorAttachment.getIds(attachments);
      if(Collections.isEmpty(idSet)) {
        attachmentRepo.deleteWithVendorBillId(company.getId(), vendorBillId);
      } else {
        attachmentRepo.deleteOrphan(company.getId(), vendorBillId, idSet);
      }
    }
    return attachments;
  }

  public boolean updateOpsStatus(ClientInfo client , Company company, Long id, TMSOperations.TMSOperationsStatus status) {
    repo.updateOpsStatus(company.getId(), id, status);
    return true;
  }

  //create tms project

  public TMSVendorBillProjectModel getTMSVendorBillProjectTask(ClientInfo client , Company company, Task task) {
    Project project = getTMSVendorBillProject(client, company);
    if(task.isNew()) {
      task = taskLogic.newTask(client, company, project.getCode(), task);
    } else {
      task = taskLogic.getTask(client, company, task.getId());
    }
    return new TMSVendorBillProjectModel(project, task);
  }

  private Project getTMSVendorBillProject(ClientInfo client , Company company) {
    Project project = projectogic.getProject(client, company, TMSVendorBill.PROJECT);
    if(project != null) {
      return project;
    }

    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    String projectSpaceCode = "tms-project";
    ProjectSpace projectSpace = projectSpaceLogic.getProjectSpace(client, company, projectSpaceCode);
    if(projectSpace == null) {
      projectSpace = new ProjectSpace(projectSpaceCode, "Data TP TMS");
      projectSpace.withOwner(employee);
      projectSpace = projectSpaceLogic.saveProjectSpace(client, company, projectSpace);
    }
    project = new Project(TMSVendorBill.PROJECT);
    project.setLabel("Vendor Bill Reconcilation");
    project.setPluginType("project:vendor-bill");
    project.withProjectSpace(projectSpace);
    project = projectogic.newProject(client, company, project);
    project = projectogic.saveProject(client, company, project);
    return project;
  }

  public TMSVendorBill updateVendorCostStatus(ClientInfo client, Company company, Long id, TMSVendorBill.VendorCostStatus vendorCostStatus) {
    TMSVendorBill vendorBill = repo.getById(company.getId(), id);
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    TMSBill tmsBill = tmsBillLogic.getTMSBillById(client, company, vendorBill.getTmsBillId());
    if (tmsBill != null && ( vendorCostStatus == TMSVendorBill.VendorCostStatus.MANUAL_CONFIRM)) {
      vendorBill.setFixed(tmsBill.getTmsBillFee().getFixedPayment());
      vendorBill.setExtra(tmsBill.getTmsBillFee().getExtraPayment());
      vendorBill.setCost(tmsBill.getTmsBillFee().getTotalPayment());
    }
    vendorBill.setAccountIdConfirmed(account.getId());
    vendorBill.setAccountFullNameConfirmed(account.getFullName());
    vendorBill.setVendorCostStatus(vendorCostStatus);
    vendorBill.set(client, company);
    return repo.save(vendorBill);
  }
  
  public TMSVendorBill loadVendorBillInfo(ClientInfo client, Company company, Long id) {
    TMSVendorBill vendorBill = getVendorBill(client, company, id);
    List<TMSVendorBillTracking> billTrackings = vendorBill.getBillTrackings();
    for(TMSVendorBillTracking tracking : billTrackings) {
      Long gpsTrackingReportId = tracking.getGpsTrackingReportId();
      if(gpsTrackingReportId != null) {
        GPSTrackingReport gpsReport = gpsTrackingLogic.getById(client, company, gpsTrackingReportId);
        tracking.setGpsReport(gpsReport);
      }
    }
    return vendorBill;
  }

  public List<TMSVendorBill> saveTMSVendorBillMapObjects(ClientInfo client, Company company, List<MapObject> records) {
    List<TMSVendorBill> vendorBills = new ArrayList<>();

    for(MapObject record : records) {
      String tmsBillCode      =  record.getString("Code");
      String fileNo           =  record.getString("File No.");
      String containerNo      =  record.getString("Container No", "");
      String licensePlate     =  record.getString("License Plate", "");
      double fixed            =  record.getDouble("Cước (Thầu phụ)", 0D);
      double extra            =  record.getDouble("Cước phát sinh (Thầu phụ)", 0D);
      double total            = record.getDouble("Tổng (Thầu phụ)", 0D);

      TMSBill bill = tmsBillLogic.getTMSBillByCode(client, company, tmsBillCode);
      if(bill == null) {
        bill = new TMSBill();
        bill.setTmsBillForwarderTransport(new TMSBillForwarderTransport());
      }
      TMSBillForwarderTransport transport = bill.getTmsBillForwarderTransport();
      String uploadError = "";
      if(!containerNo.equals(transport.getContainerNo())) {
        if(StringUtil.isNotBlank(containerNo) || StringUtil.isNotBlank(transport.getContainerNo()))
          uploadError = "Số CONT NO không khớp : " + containerNo + " - " + transport.getContainerNo();
      }
      TMSVendorBill vendorBill = this.getByTMSBillId(client, company, bill.getId());
      if(vendorBill == null) {
        vendorBill = new TMSVendorBill();
        if(bill.isNew()) {
          vendorBill.setFileNo(fileNo);
        } else {
          vendorBill.setFileNo(bill.getLabel());
          vendorBill.setTmsBillId(bill.getId());
        }
      }

      String [] lPs = licensePlate.split(",");
      List<String> licensePlates = new ArrayList<>();
      for(String lp : lPs) {
        lp = lp.replaceAll(" ", "").replaceAll(".", "").replaceAll("-", "");
        licensePlates.add(lp);
      }
      
      List<TMSVendorBillTracking> billTrackings = vendorBill.getBillTrackings();
      if(billTrackings.size() == 0 && licensePlates.size() > 0) {
        uploadError += "Số biển số không khớp : " + licensePlates + " - Empty" + "\n";
      }
      for(TMSVendorBillTracking tracking : billTrackings) {
        String lp = tracking.getLicensePlate();
        if(StringUtil.isBlank(lp)) continue;
        lp = lp.replaceAll(" ", "").replaceAll(".", "").replaceAll("-", "");
        if(licensePlates.contains(lp)) continue;
        uploadError += "Số biển số không khớp : " + licensePlates + " - " + lp + "\n";
      }
      
      if(StringUtil.isBlank(uploadError)) uploadError = null;
      if(bill.isNew()) uploadError = "TMSBill không khớp :" + tmsBillCode;
      vendorBill.setUploadError(uploadError);
      
      vendorBill.setXlsxLastUpdate(new Date());
      vendorBill.setFixed(fixed);
      vendorBill.setExtra(extra);
      vendorBill.setCost(fixed + extra);
      if(bill != null && bill.getTmsBillFee() != null) {
        TMSBillFee billFee = bill.getTmsBillFee();
        if(billFee.getTotalPayment() != 0 && billFee.getFixedPayment() == vendorBill.getFixed() && billFee.getExtraPayment() == vendorBill.getExtra() && billFee.getTotalPayment() == vendorBill.getCost()) {
          vendorBill.setVendorCostStatus(TMSVendorBill.VendorCostStatus.AUTO_CONFIRM);
        } else if(billFee.getTotalPayment() == 0 && billFee.getFixedPayment() == vendorBill.getFixed() && billFee.getExtraPayment() == vendorBill.getExtra() && billFee.getTotalPayment() == vendorBill.getCost()) {
          vendorBill.setVendorCostStatus(null);
        } else {
          vendorBill.setVendorCostStatus(TMSVendorBill.VendorCostStatus.NEED_CONFIRM);
        }
      }
      this.saveVendorBill(client, company, vendorBill);
      vendorBills.add(vendorBill);
    }
    return vendorBills;
  }
}