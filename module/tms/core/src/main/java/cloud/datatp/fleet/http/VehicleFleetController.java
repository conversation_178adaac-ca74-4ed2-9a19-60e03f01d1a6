package cloud.datatp.fleet.http;

import java.util.List;
import java.util.concurrent.Callable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cloud.datatp.fleet.vehicle.VehicleFleetService;
import cloud.datatp.fleet.vehicle.entity.Transporter;
import cloud.datatp.fleet.vehicle.entity.TransporterMembership;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetCoordinator;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetMembership;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import net.datatp.module.company.http.BaseCompanyController;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.backend.BackendResponse;
import net.datatp.module.session.HttpClientSessionService;

@ConditionalOnBean(HttpClientSessionService.class)
@Api(value = "openfreightone", tags = {"logistics/fleet"})
@RestController
@RequestMapping("/rest/v1.0.0/logistics/fleet")
public class VehicleFleetController extends BaseCompanyController {

  @Autowired
  private VehicleFleetService service;
  
  protected VehicleFleetController() {
    super("fleet", "fleet");
  }

  //Vehicle Fleet
  
  @ApiOperation(value = "Change the vehicle fleets storage state", response = Boolean.class)
  @PutMapping("vehicle-fleet/storage-state")
  public @ResponseBody BackendResponse changeVehicleFleetStorageState(HttpServletRequest httpReq, @RequestBody ChangeStorageStateRequest req) {
    Callable<Boolean> executor = () -> {
      return service.changeVehicleFleetStorageState(getAuthorizedClientInfo(httpReq), req);
    };
    return  execute(Method.PUT, "vehicle-fleet/storage-state", executor);
  }


  //Transporter
  @ApiOperation(value ="Get By Id", response = Transporter.class)
  @GetMapping("transporter/id/{id}")
  public @ResponseBody BackendResponse getTransporterById(HttpServletRequest httpReq, @PathVariable("id") Long id) {
    Callable<Transporter> executor = () -> {
      ClientContext ctx = getClientContext(httpReq);
      return service.loadTransporterById(ctx.getClientInfo(), ctx.getCompany(), id);
    };
    return execute(Method.GET, "transporter/id/{id}", executor);
  }
  
  @ApiOperation(value ="Get By Transporter Code", response = Transporter.class)
  @GetMapping("transporter/{code}")
  public @ResponseBody BackendResponse getTransporterByCode(HttpServletRequest httpReq, @PathVariable("code") String code) {
    Callable<Transporter> executor = () -> {
      ClientContext ctx = getClientContext(httpReq);
      return service.loadTransporterByCode(ctx.getClientInfo(), ctx.getCompany(), code);
    };
    return execute(Method.GET, "transporter/{code}", executor);
  }
  
  @ApiOperation(value = "Save Transporter", response = Transporter.class)
  @PutMapping("transporter")
  public @ResponseBody BackendResponse saveTransporter(HttpServletRequest httpReq, @RequestBody Transporter type) {
    Callable<Transporter> executor = () -> {
      ClientContext ctx = getClientContext(httpReq);
      return service.saveTransporter(ctx.getClientInfo(), ctx.getCompany(), type);
    };
    return execute(Method.PUT, "transporter", executor);
  }
  
  @ApiOperation(value = "Change the transporters storage state", response = Boolean.class)
  @PutMapping("transporter/storage-state")
  public @ResponseBody BackendResponse changeTransportersStorageState(HttpServletRequest httpReq, @RequestBody ChangeStorageStateRequest req) {
    Callable<Boolean> executor = () -> {
      return service.changeTransportersStorageState(getAuthorizedClientInfo(httpReq), req);
    };
    return  execute(Method.PUT, "transporter/storage-state", executor);
  }

  @ApiOperation(value = "Add transporter membership", responseContainer = "List", response = TransporterMembership.class)
  @PostMapping("transporter-membership")
  public @ResponseBody BackendResponse addTransporterMembership(HttpServletRequest httpReq, @RequestBody TransporterMembership membership) {
    Callable<TransporterMembership> executor = () -> {
      ClientContext ctx = getClientContext(httpReq);
      return service.addTransporterMembership(ctx.getClientInfo(), ctx.getCompany(), membership);
    };
    return execute(Method.POST, "transporter-membership", executor);
  }

  @ApiOperation(value = "Remove transporter memberships", response = Boolean.class)
  @DeleteMapping("transporter-membership/delete")
  public @ResponseBody BackendResponse removeTransporterMemberships(HttpServletRequest httpReq, @RequestBody List<TransporterMembership> memberships) {
    Callable<Boolean> executor = () -> {
      ClientContext ctx = getClientContext(httpReq);
      return service.removeTransporterMemberships(ctx.getClientInfo(), ctx.getCompany(), memberships);
    };
    return  execute(Method.DELETE, "transporter-membership/delete", executor);
  }
}