package migration.db.crm

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil

import java.sql.Connection

public class AlterTableSet extends DBMigrationRunnableSet {
  public AlterTableSet() {
    super("""Alter CRM Tables""");

    String label = """Alter CRM Tables""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_service_inquiry DROP COLUMN IF EXISTS edit_mode;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_service_inquiry DROP COLUMN IF EXISTS cc_type;")
          connUtil.execute("DROP INDEX IF EXISTS public.lgc_sales_specific_service_inquiry_code_idx;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_service_inquiry DROP COLUMN IF EXISTS code;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_service_inquiry DROP COLUMN IF EXISTS status;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_service_inquiry DROP CONSTRAINT IF EXISTS fkjbxrngc1tic1lvnu16lmcyh8u;")
          connUtil.execute("ALTER TABLE public.lgc_sales_cargo DROP CONSTRAINT IF EXISTS fk71r2c09qeoowcyb3fgjb6febf;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_service_inquiry ALTER COLUMN volume_cbm DROP NOT NULL;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_service_inquiry DROP COLUMN IF EXISTS task_code;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_quotation DROP COLUMN IF EXISTS shareable;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_quotation DROP CONSTRAINT IF EXISTS lgc_sales_specific_quotation_code;")
          connUtil.execute("DROP INDEX IF EXISTS public.idxopphmtuql36ufgclljrdvxve8;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_quotation DROP COLUMN IF EXISTS code;")
          connUtil.execute("ALTER TABLE public.lgc_sales_specific_quotation DROP COLUMN IF EXISTS request_reference;")
          connUtil.execute("ALTER TABLE public.lgc_sales_booking DROP CONSTRAINT IF EXISTS lgc_sales_booking_code;")
          connUtil.execute("DROP INDEX IF EXISTS public.idxnyidgk2rblv1mo8qw3q6m4yuf;")
          connUtil.execute("DROP INDEX IF EXISTS public.idxavgmbate7rqyhyggyeray8cko;")
          connUtil.execute("ALTER TABLE public.lgc_sales_booking DROP COLUMN IF EXISTS code;")
        }
    };
    addRunnable(alterTables);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;

Connection conn = shellContext.getPrimaryDBConnection();
DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

AlterTableSet migration = new AlterTableSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"