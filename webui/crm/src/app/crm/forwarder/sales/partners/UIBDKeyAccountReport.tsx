
import React from 'react';
import * as FeatherIcon from 'react-feather';

import { bs, app, input, server, util, entity } from '@datatp-ui/lib';
import {
  UISalemanKeyAccountReportList,
  UISalemanKeyAccountReportPlugin
} from './UISalemanKeyAccountReportList';
import {
  UIVolumeSalemanKeyAccountReportList,
  UIVolumeSalemanKeyAccountReportPlugin
} from './UIVolumeSalemanKeyAccountReportList';
import { WKeyAccountReportTypeSelector, WQuickTimeRangeSelector } from '../dashboard/UIDashboardUtility';
import { GridConfig, ResponsiveGrid } from '../dashboard/ResponsiveGrid';

const SESSION = app.host.DATATP_HOST.session;

const USD_CURR_FORMAT = (val: any) => util.text.formater.currency(val, 2);


interface UIGridReportProps extends app.AppComponentProps {
  rawRecords: Array<any>;
  title?: string;
  type: 'FCL' | 'AIR' | 'OTHER'
}

class UIPerformanceGridReport extends bs.AvailableSize<UIGridReportProps> {
  config: GridConfig

  constructor(props: UIGridReportProps) {
    super(props);
    const { title, type } = this.props;
    let volumeLabel = 'Volume';
    if (type === 'FCL') {
      volumeLabel = 'TEUs';
    } else if (type === 'AIR') {
      volumeLabel = 'GW (KGS)'
    }

    this.config = {
      header: {
        height: 20,
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        { field: 'keyAccountName', label: `${title ? title : 'Key Account'}`, cssClass: 'border-start border-light text-start' },
        { field: 'volume', label: volumeLabel, cssClass: 'border-start border-light text-start' },
        { field: 'revenue', label: 'Revenue (USD)', cssClass: 'border-start border-light text-start' },
        { field: 'profit', label: 'Profit (USD)', cssClass: 'border-start border-light text-start' },
      ],
      widthConfig: {
        totalWidth: 1500,
        minColumnWidth: 150,
        ratios: [4, 1, 1, 1]
      }
    }
  }

  renderContent(width: number | string, _height: number): React.ReactElement {
    const { rawRecords } = this.props;
    const contentWidth = typeof width === 'number' ? width : 500;

    return (
      <div className="" style={{ width: contentWidth }}>
        <ResponsiveGrid className="w-100 h-100" config={this.config} data={rawRecords} />
      </div>
    );
  }
}

class ReportBean {
  id: number | undefined;
  type: 'BD' | 'SALES';
  code: string;
  suggestionOrRequest: string;
  salemanAccountId: number;
  salemanLabel: string;
  reportedDateFrom: string;
  reportedDateTo: string;
  volumePerformance: any;
  highlights: {
    signedAaContracts: string,
    meetingIn2Weeks: string,
    newAgentsApproachedIn2Weeks: string,
    otherHighlights: string,
    lowlights: string
  };
  forecast: {
    airVolume: string,
    seaVolume: string,
    estimate: string
  };
}

export interface UIBDKeyAccountReportProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UIBDKeyAccountReport extends app.AppComponent<UIBDKeyAccountReportProps> {
  reportBean: ReportBean = {
    id: undefined,
    type: 'SALES',
    salemanAccountId: SESSION.getAccountId(),
    salemanLabel: SESSION.getAccountAcl().getFullName(),
    reportedDateFrom: '',
    reportedDateTo: '',
    code: '',
    suggestionOrRequest: '',
    volumePerformance: {},
    highlights: {
      signedAaContracts: '',
      meetingIn2Weeks: '',
      newAgentsApproachedIn2Weeks: '',
      otherHighlights: '',
      lowlights: ''
    },
    forecast: {
      airVolume: '',
      seaVolume: '',
      estimate: ''
    }
  }

  dateFilterLabel: string = 'This month';
  viewId: number = util.IDTracker.next();

  constructor(props: UIBDKeyAccountReportProps) {
    super(props);
  }

  initialReportBean() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    firstDayOfMonth.setHours(0, 0, 0, 0);
    lastDayOfMonth.setHours(23, 59, 59, 999);

    this.reportBean = {
      id: undefined,
      type: 'SALES',
      reportedDateFrom: util.TimeUtil.javaCompactDateTimeFormat(firstDayOfMonth),
      reportedDateTo: util.TimeUtil.javaCompactDateTimeFormat(lastDayOfMonth),
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      code: '',
      suggestionOrRequest: '',
      volumePerformance: {},
      highlights: {
        signedAaContracts: '',
        meetingIn2Weeks: '',
        newAgentsApproachedIn2Weeks: '',
        otherHighlights: '',
        lowlights: ''
      },
      forecast: {
        airVolume: '',
        seaVolume: '',
        estimate: ''
      }
    }
    this.dateFilterLabel = 'This Month';
    this.loadData();
  }

  componentDidMount(): void {
    this.loadLatestReport();
  }

  loadLatestReport() {
    const { appContext } = this.props;
    this.markLoading(true);

    appContext.createHttpBackendCall('PartnerReportService', 'getLatestReportBySaleman')
      .withSuccessData((latestReport: any) => {
        if (latestReport) {
          this.reportBean = latestReport;
          this.dateFilterLabel = 'Custom';
        } else {
          this.initialReportBean();
        }
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        if (!this.reportBean) {
          this.initialReportBean();
        }
        return;
      })
      .call()
  }

  rawDataProcessor(rawData: Array<any> = [], serviceType: string): Array<any> {
    const groupedByKeyAccount = rawData.reduce((acc, record) => {
      const keyAccountName = record.keyAccountName;
      if (!acc[keyAccountName]) {
        acc[keyAccountName] = {
          keyAccountName,
          volume: 0,
          revenue: 0,
          cost: 0
        };
      }
      const revenue = (record.subtotalSellingVnd + record.subtotalOtherDebitVnd) / (record.exchangeRateUsd || 1) || 0;
      const cost = (record.subtotalBuyingVnd + record.subtotalOtherCreditVnd) / (record.exchangeRateUsd || 1) || 0;
      acc[keyAccountName].revenue += (revenue || 0);
      acc[keyAccountName].cost += (cost || 0);
      acc[keyAccountName].volume += (record.volume || 0);
      return acc;
    }, {});

    let holder: Array<any> = Object.values(groupedByKeyAccount).map((group: any) => ({
      keyAccountName: group.keyAccountName,
      volume: group.volume,
      profit: group.revenue - group.cost,
      revenue: group.revenue,
    })).sort((a, b) => b.profit - a.profit);

    if (holder.length === 0) return holder;

    let sumAll: { volume: number, profit: number, revenue: number } = holder.reduce(
      (acc, curr) => {
        acc.volume += curr.volume || 0;
        acc.profit += curr.profit || 0;
        acc.revenue += curr.revenue || 0;
        return acc;
      },
      { volume: 0, profit: 0, revenue: 0 }
    );

    const totalRecord: any = {
      keyAccountName: 'Total',
      profit: sumAll.profit,
      revenue: sumAll.revenue,
    };


    if (serviceType === 'lcl') {
      totalRecord.volume = sumAll.volume.toString() + ' CBM';
    } else if (serviceType === 'other') {
      totalRecord.volume = sumAll.volume.toString() + ' SHPT';
    } else {
      totalRecord.volume = sumAll.volume;
    }

    holder.push(totalRecord);

    for (let rec of holder) {
      rec['profit'] = USD_CURR_FORMAT(rec.profit)
      rec['revenue'] = USD_CURR_FORMAT(rec.revenue)
      rec['volume'] = USD_CURR_FORMAT(rec.volume)
    }
    return holder;

  }

  loadData() {
    const { appContext } = this.props;
    this.markLoading(true);

    const { reportedDateFrom, reportedDateTo } = this.reportBean;

    const params: any = {
      params: {
        fromDate: reportedDateFrom,
        toDate: reportedDateTo,
      }
    };

    appContext.createHttpBackendCall('PartnerReportService', 'searchVolumeSalemanKeyAccountReport', { params })
      .withSuccessData((records: Array<any>) => {
        const airHolder: Array<any> = [];
        const fclHolder: Array<any> = [];
        const lclHolder: Array<any> = [];
        const otherHolder: Array<any> = [];

        for (let rec of records) {
          const mapRec: any = { ...rec };
          if (rec.shipmentType === 'FREE-HAND') {
            mapRec.keyAccountCode = rec.customerCode;
            mapRec.keyAccountName = rec.customerName;
          } else {
            mapRec.keyAccountCode = rec.agentCode;
            mapRec.keyAccountName = rec.agentName;
          }

          if (['AirExpTransactions', 'AirImpTransactions'].includes(mapRec['typeOfService'])) {
            mapRec.volume = mapRec.hawbGw;
            airHolder.push(mapRec);
          } else if (['SeaExpTransactions_FCL', 'SeaImpTransactions_FCL'].includes(mapRec['typeOfService'])) {
            const containerSize = mapRec.containerSize || '';
            const teusMatch = containerSize.match(/(\d+)X(\d+)/);
            if (teusMatch) {
              const containers = parseInt(teusMatch[1]);
              const size = parseInt(teusMatch[2]);
              mapRec.volume = containers * (size === 20 ? 1 : 2);
            }
            fclHolder.push(mapRec);
          } else if (['SeaExpTransactions_CSL', 'SeaImpTransactions_CSL', 'SeaExpTransactions_LCL', 'SeaImpTransactions_LCL'].includes(mapRec['typeOfService'])) {
            mapRec.volume = mapRec.hawbCbm;
            lclHolder.push(mapRec);
          } else {
            mapRec.volume = mapRec.customsNumberCount || 0;
            otherHolder.push(mapRec);
          }
        }

        let volumePerformance: any = {
          airService: this.rawDataProcessor(airHolder, 'air'),
          fclService: this.rawDataProcessor(fclHolder, 'fcl'),
          otherService: [...this.rawDataProcessor(lclHolder, 'lcl'), ...this.rawDataProcessor(otherHolder, 'other')],
          totalProfit: 0,
          totalRevenue: 0
        };

        const airService: Array<any> = volumePerformance['airService'] || [];
        const fclService: Array<any> = volumePerformance['fclService'] || [];
        const otherService: Array<any> = volumePerformance['otherService'] || [];

        const allSegments = [...airService, ...fclService, ...otherService];
        volumePerformance.totalProfit = Number(allSegments
          .filter(record => record.keyAccountName !== 'Total')
          .reduce((sum, record) => {
            const profit = typeof record.profit === 'string'
              ? Number(record.profit.replace(/[^0-9.-]+/g, ''))
              : (record.profit || 0);
            return sum + profit;
          }, 0).toFixed(2));

        volumePerformance.totalRevenue = Number(allSegments
          .filter(record => record.keyAccountName !== 'Total')
          .reduce((sum, record) => {
            const revenue = typeof record.revenue === 'string'
              ? Number(record.revenue.replace(/[^0-9.-]+/g, ''))
              : (record.revenue || 0);
            return sum + revenue;
          }, 0).toFixed(2));

        this.reportBean['volumePerformance'] = volumePerformance;
        this.markLoading(false);
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .withFail((response: server.BackendResponse) => {
        let messageError: string = response.error['message'] || 'An unexpected error occurred. Please try again later.';
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{messageError}
          </div>,
          { backdrop: 'static', size: 'md' }
        );
        return;
      })
      .call();
  }

  onSave = () => {
    let { appContext } = this.props;
    appContext.createHttpBackendCall('PartnerReportService', 'saveSalemanKeyAccountReport', { report: this.reportBean })
      .withEntityOpNotification('commit', 'Sale Account Report')
      .withSuccessData((reportBeanInDb: any) => {
        this.reportBean = reportBeanInDb
        this.viewId = util.IDTracker.next();
        this.forceUpdate();
      })
      .call();
  }

  onViewAllReports = () => {
    let { pageContext, space } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onSelect = (appCtx: app.AppContext, pageCtx: app.PageContext, entity: any) => {

        let volumePerformance: any = typeof entity['volumePerformance'] === 'string'
          ? JSON.parse(entity['volumePerformance'])
          : (entity['volumePerformance'] || {});
        entity['volumePerformance'] = volumePerformance;

        let forecast: any = typeof entity['forecast'] === 'string'
          ? JSON.parse(entity['forecast'])
          : (entity['forecast'] || {});
        entity['forecast'] = forecast;

        let highlights: any = typeof entity['highlights'] === 'string'
          ? JSON.parse(entity['highlights'])
          : (entity['highlights'] || {});
        entity['highlights'] = highlights;

        pageCtx.back();
        this.reportBean = entity;
        this.forceUpdate();
      }

      return (
        <UISalemanKeyAccountReportList
          appContext={appCtx} pageContext={pageCtx} plugin={new UISalemanKeyAccountReportPlugin(space)}
          onSelect={onSelect} />
      )
    }
    pageContext.createPopupPage('saleman-account-report', "Saleman Key Account Report", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onNewReport = () => {
    this.initialReportBean();
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  renderUIPerformance(type: 'FCL' | 'AIR' | 'OTHER', title: string, records: Array<any> = []) {
    if (records.length === 0) return <></>
    const { appContext, pageContext } = this.props;
    return (
      <UIPerformanceGridReport type={type} title={title}
        appContext={appContext} pageContext={pageContext} rawRecords={records} />
    )
  }

  onViewAllPerformance = () => {
    let { pageContext } = this.props;
    const { reportedDateFrom, reportedDateTo } = this.reportBean;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVolumeSalemanKeyAccountReportList
          appContext={appCtx} pageContext={pageCtx}
          plugin={new UIVolumeSalemanKeyAccountReportPlugin().withReportedDate(reportedDateFrom, reportedDateTo)} />
      )
    }
    pageContext.createPopupPage('performance', "Performance", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onModifyHighlights = (bean: any | Array<any>, _field: string, _oldVal: any, newVal: any) => {
    let highlights: any = this.reportBean['highlights'] || {}
    this.reportBean['highlights'] = highlights;
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  onModifyForecast = (bean: any | Array<any>, _field: string, _oldVal: any, newVal: any) => {
    let forecast: any = this.reportBean['forecast'] || {}
    this.reportBean['forecast'] = forecast;
    this.viewId = util.IDTracker.next();
    this.forceUpdate();
  }

  renderReportHeader() {
    const { pageContext, appContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let reportCode: string = this.reportBean['code'] || `NEW`;
    const reportSalemanLabel: string = this.reportBean['salemanLabel'] || 'N/A'

    return (
      <div className='flex-hbox flex-grow-0 w-100 justify-content-between align-items-center bg-white my-1 rounded-sm' key={this.reportBean.type} >
        <div className='flex-hbox align-items-center justify-content-start mx-2 py-1'>
          <h5 className='mx-2 fs-9' style={{ color: '#6c757d' }}>{`${reportSalemanLabel}`}</h5>
          <h6 className='px-2 fs-9' style={{ color: '#6c757d' }}>{`(${reportCode})`}</h6>
        </div>
        <div className='flex-hbox align-items-center justify-content-end mx-2 gap-2'>

          <WKeyAccountReportTypeSelector appContext={appContext} pageContext={pageContext}
            reportType={this.reportBean.type} readOnly={bs.ScreenUtil.isMobileScreen() || !writeCap || !!this.reportBean['id']}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportBean.type = bean;
              this.viewId = util.IDTracker.next();
              this.forceUpdate();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
            onClick={this.onNewReport} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
            <FeatherIcon.Plus size={14} className="me-1" /> New Report
          </bs.Button>

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.onViewAllReports} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
            View all
            <FeatherIcon.ChevronRight size={14} className="ms-2" />
          </bs.Button>

        </div>
      </div>
    )
  }

  render() {

    const { appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();

    const volumePerformance: any = this.reportBean['volumePerformance'] || {}
    const fclRecords: Array<any> = volumePerformance['fclService'] || []
    const airRecords: Array<any> = volumePerformance['airService'] || []
    const otherRecords: Array<any> = volumePerformance['otherService'] || []

    const totalProfit: number = (volumePerformance['totalProfit'] || 0)
    const totalRevenue: number = (volumePerformance['totalRevenue'] || 0)

    const highlights: any = this.reportBean['highlights'] || {}
    const forecast: any = this.reportBean['forecast'] || {}

    let profitRate: number = totalProfit / totalRevenue || 0;
    let margin = util.text.formater.currency(profitRate * 100, 2)

    const isOwnerReport: boolean = this.reportBean['salemanAccountId'] === SESSION.getAccountId();

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    const dateFilter = {
      label: this.dateFilterLabel,
      fromValue: this.reportBean.reportedDateFrom,
      toValue: this.reportBean.reportedDateTo
    };

    return (
      <div className="flex-vbox mx-2 bg-white rounded-md" >

        {this.renderReportHeader()}

        <div className="flex-vbox border-top bg-body-highlight" key={this.viewId}>
          <bs.GreedyScrollable className="my-1">


            {/* ------------------- Key Account Performance ----------------------- */}
            <div className="flex-vbox mb-1 p-1">
              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
                    <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Key Account Performance</h5>

                    <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

                      <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
                        initBean={dateFilter}
                        onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
                          this.dateFilterLabel = bean.label;
                          this.reportBean.reportedDateFrom = bean.fromValue;
                          this.reportBean.reportedDateTo = bean.toValue;
                          this.loadData();
                        }} />

                      <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                        onClick={() => { }}>
                        <FeatherIcon.RefreshCcw size={14} className="me-1" />
                        Refresh
                      </bs.Button>

                      <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                        onClick={() => { }}>
                        <FeatherIcon.Download size={14} className="me-1" />
                        Export
                      </bs.Button>

                      <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                        onClick={this.onViewAllPerformance}>
                        <FeatherIcon.Maximize2 size={14} className="me-1" />
                        Expand
                      </bs.Button>

                    </div>
                  </div>

                  <div className="flex-vbox" style={{ minHeight: 300 }}>
                    {this.renderUIPerformance('AIR', 'AIRFREIGHT', airRecords)}
                    {this.renderUIPerformance('FCL', 'SEAFREIGHT', fclRecords)}
                    {this.renderUIPerformance('OTHER', 'OTHER SERVICES', otherRecords)}
                  </div>

                  <div className="flex-grow-0 flex-shrink-0 p-2 border-top flex-hbox justify-content-between align-items-center">

                    <div className='flex-hbox flex-grow-0 flex-shrink-0 align-items-center justify-content-start mx-2 py-1' style={{ maxWidth: 250 }}>
                      <h6 className="fw-bold text-danger"> <FeatherIcon.Package className="me-2" size={16} />TOTAL PERFORMANCE</h6>
                    </div>

                    <div className='flex-hbox align-items-center justify-content-end mx-2 gap-2'>

                      <div className="d-flex justify-content-between align-items-center flex-grow-0 flex-shrink-0 mb-1 pe-2 fs-9 border-end">
                        <span className="fw-bold me-2">GP:</span>
                        <span>{`${USD_CURR_FORMAT(totalProfit)} USD`}</span>
                      </div>

                      <div className="d-flex justify-content-between align-items-center flex-grow-0 flex-shrink-0 mb-1 pe-2 fs-9 border-end">
                        <span className="fw-bold me-2">REV:</span>
                        <span>{`${USD_CURR_FORMAT(totalRevenue)} USD`}</span>
                      </div>

                      <div className="d-flex justify-content-between align-items-center flex-grow-0 flex-shrink-0 mb-1 pe-2 fs-9 text-primary">
                        <span className="fw-bold me-2">Margin (GP/ REV):</span>
                        <span>{`${margin}  %`}</span>
                      </div>

                    </div>
                  </div>

                </div>
              </div>
            </div>


            {/* ------------------- Highlights ----------------------- */}
            <div className="flex-hbox align-items-start justify-content-between flex-grow-0 flex-shrink-0 p-1 mb-1 gap-1">

              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>

                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
                    <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Award className="me-2" size={18} />HIGHLIGHTS</h5>
                  </div>

                  <div className="flex-vbox">
                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.FileText className="me-2 text-primary" size={14} />
                          <span>Signed AA/Contracts</span>
                        </div>
                        <input.BBTextField bean={highlights} field="signedAaContracts" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">Signed AA/Contracts:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['signedAaContracts'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.Calendar className="me-2 text-info" size={14} />
                          <span>Meeting in 2 weeks</span>
                        </div>
                        <input.BBTextField bean={highlights} field="meetingIn2Weeks" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">Meeting in 2 weeks:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['meetingIn2Weeks'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.Users className="me-2 text-success" size={14} />
                          <span>New Agents approached in 2 weeks</span>
                        </div>
                        <input.BBTextField bean={highlights} field="newAgentsApproachedIn2Weeks" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">New Agents approached in 2 weeks:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['newAgentsApproachedIn2Weeks'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <FeatherIcon.Star className="me-2 text-warning" size={14} />
                          <span>Other highlights</span>
                        </div>
                        <input.BBTextField bean={highlights} field="otherHighlights" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">Other highlights:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['otherHighlights'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>

                    <bs.CssTooltip position='top-right' width={400} offset={{ x: 200, y: 10 }}>
                      <bs.CssTooltipToggle className='flex-vbox justify-content-start px-0 py-1 w-100'>
                        <div className='flex-grow-0 flex-hbox justify-content-start align-items-center w-100'>
                          <h6 className="fw-bold py-2"><FeatherIcon.AlertTriangle className="me-2" size={16} />LOWLIGHTS</h6>
                        </div>
                        <input.BBTextField bean={highlights} field="lowlights" style={{ height: '4em' }}
                          onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => this.forceUpdate()} />
                      </bs.CssTooltipToggle>
                      <bs.CssTooltipContent className="d-flex flex-column rounded" >
                        <div className="tooltip-header mb-2">
                          <span className="tooltip-title">LOWLIGHTS:</span>
                        </div>
                        <ul className="mb-2 ps-3">
                          {(highlights['lowlights'] || '...').split('\n').map((line: string, i: number) => (
                            <li key={i}>{line}</li>
                          ))}
                        </ul>
                      </bs.CssTooltipContent>
                    </bs.CssTooltip>
                  </div>
                </div>
              </div>

              {/* ------------------- Forecast | Suggestion/ Request ----------------------- */}
              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center gap-1"
                style={{
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>

                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100" style={{ minHeight: 250 }}>
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
                    <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.Star className="me-2" size={18} />FORECAST</h5>
                  </div>

                  <div className="flex-vbox">
                    <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                      <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                        <FeatherIcon.Star className="me-2 text-warning" size={12} />
                        <span className="text-nowrap">Air Volume</span>
                      </div>
                      <input.BBStringField bean={forecast} field="airVolume" />
                    </div>

                    <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                      <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                        <FeatherIcon.Star className="me-2 text-warning" size={12} />
                        <span className="text-nowrap">Sea Volume</span>
                      </div>

                      <input.BBStringField bean={forecast} field="seaVolume" />
                    </div>

                    <div className="flex-hbox justify-content-start align-items-center px-0 py-1">
                      <div className='flex-hbox justify-content-start align-items-center flex-grow-0 me-2' style={{ minWidth: 130 }}>
                        <FeatherIcon.Star className="me-2 text-warning" size={12} />
                        <span className='text-nowrap'>Estimate</span>
                      </div>
                      <input.BBStringField bean={forecast} field="estimate" />
                    </div>
                  </div>

                </div>

                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100 mt-2">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between border-bottom px-2 py-1">
                    <h5 style={{ color: '#6c757d', fontSize: '0.9rem' }}><FeatherIcon.MessageCircle className="me-2" size={18} />SUGGESTION/ REQUEST</h5>
                  </div>

                  <div className="flex-vbox p-2">
                    <input.BBTextField bean={this.reportBean} field="suggestionOrRequest" style={{
                      height: 170,
                      fontSize: '1rem',
                    }}
                      placeholder='Any suggestion/ request for improvement?' />
                  </div>
                </div>
              </div>
            </div>
          </bs.GreedyScrollable>
        </div>

        <bs.Toolbar className='border' hide={!writeCap || !isOwnerReport}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            label={'Save'} onClick={this.onSave} />
        </bs.Toolbar>
      </div>
    )
  }
}
