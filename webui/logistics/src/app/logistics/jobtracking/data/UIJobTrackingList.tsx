import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, grid, app, entity, server, sql, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { T } from '../Dependency';
import { CellDataInputType, CellDataType, JobTrackingListConfig, StepStatus } from '../config/JobTrackingListConfig';
import { UIReportForm } from './UIJobTrackingReport';
import { JobTrackingVGridPlugin, JobTrackingVGridPluginManager } from '../config/plugin/JobTrackingVGridPlugin';
import { renderSummaryOfStepsConfig } from './UIJobTrackingDetailForm';
import { JobTrackingPermissionHelper } from '../config/JobTrackingPermissionHelper';
import { UIJobTrackingIssueList, UIJobTrackingIssuePlugin } from './UIJobTrackingIssue';

class XLSXButton extends entity.XlsxExportButton {
  override customDataListExportModel = (model: entity.DataListExportModel) => {
    for (let record of model.records) {
      for (let field of model.fields) {
        if (record[field.name] && record[field.name]['status']) {
          if (record[field.name]['status'] === StepStatus.Done) record[field.name] = 'T';
          else record[field.name] = '';
        }
      }
    }

    let { context } = this.props;
    model["fileName"] = (context.config.id ? context.config.id : 'Job Tracking') + ".xlsx"
    for (let fieldGroup of model.fieldGroups) {
      if (!fieldGroup.label) fieldGroup.label = '_blank_'
    }
    return model;
  }
}
export class UIJobTrackingListPlugin extends entity.DbEntityListPlugin {
  param: any;
  view: 'editor' | 'claim';
  constructor(project: any) {
    super();

    this.backend = {
      context: 'company',
      service: 'JobTrackingService',
      changeStorageStateMethod: 'updateJobTrackingStorageState',
      searchMethod: 'searchJobTrackings'
    }

    this.searchParams = {
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "rangeFilters": [
      ],
      "params": { jobTrackingProjectId: project.id, withPermission: true },
      "maxReturn": 5000
    };

    this.param = {
      projectId: project.id,
      storageState: entity.StorageState.ACTIVE,
      withPermission: true
    }
  }

  backendChangeState(uiList: entity.DbEntityList<entity.DbEntityListProps, entity.DbEntityListState>, _targetIds: number[], newStorageState: string): void {
    let targetIds = [];
    for (let rec of this.listModel.getSelectedRecords()) {
      targetIds.push(rec.jobTrackingId);
    }

    let params = {
      'request': {
        'entityIds': targetIds,
        'newStorageState': newStorageState
      }
    };
    this.createBackendChangeState(uiList, params, targetIds).call();
  }

  withPermission = (withPermission: boolean) => {
    this.addSearchParam('withPermission', withPermission);
    return this;
  }

  withView = (view: 'editor' | 'claim') => {
    this.view = view;
    this.addSearchParam('view', view);
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }
}

export interface UIJobTrackingListProps extends entity.DbEntityListProps {
  listConfig: JobTrackingListConfig;
}
export class UIJobTrackingList extends entity.DbEntityList<UIJobTrackingListProps> {
  protected createVGridContext() {
    let { plugin } = this.props;
    let pluginImpl = plugin as UIJobTrackingListPlugin;
    let config = this.createVGridConfig();
    let context = new grid.VGridContext(this);
    context.init(config, pluginImpl.getListModel());

    let vGridPlugins: JobTrackingVGridPlugin[] = JobTrackingVGridPluginManager.getPlugins();

    for (let vGridPlugin of vGridPlugins) {
      if (!vGridPlugin) continue;
      let support = vGridPlugin.isApplyFor(context);
      if (!support) continue;
      if (vGridPlugin.toolbar) {
        if (vGridPlugin.toolbar.actions) vGridPlugin.toolbar.actions.map((act) => config.toolbar.actions?.unshift(act));
        if (vGridPlugin.toolbar.dropdownActions) config.toolbar.dropdownActions = vGridPlugin.toolbar.dropdownActions;
      }
      if (vGridPlugin.onInitVGrid) vGridPlugin.onInitVGrid(context);
    }

    return context;
  }

  getTime = (bean: any, fieldName: string): number | null => {
    let val = bean[fieldName];
    if (!val) return null;
    if (typeof val === "string") {
      const pattern = /(\d{2})\/(\d{2})\/(\d{4})/;
      val = new Date(val.replace(pattern, "$3-$2-$1"));
    }
    return val.getTime();
  }

  sort(records: Array<any>) {
    //sort algorithms
    let { listConfig } = this.props;
    let sortableColumns = listConfig.getSortableColumns();
    if (!records) return null;
    return records.sort((a, b) => {
      for (let i = 0; i < sortableColumns.length; i++) {
        const column = sortableColumns[i];
        const { dataInputType, sortDirection, name } = column;
        let valueA = a[name];
        let valueB = b[name];
        if (dataInputType === 'Date') {
          valueA = this.getTime(a, name);
          valueB = this.getTime(b, name);
        } else if (dataInputType === 'String') {
          valueA = valueA && valueA.toUpperCase();
          valueB = valueB && valueB.toUpperCase();
        }

        if (!valueA && !valueB) {
          continue;
        } else if (!valueA) {
          return sortDirection === 'ASC' ? 1 : -1; // Place at the bottom of the list
        } else if (!valueB) {
          return sortDirection === 'ASC' ? -1 : 1; // Place at the bottom of the list
        }
        if (valueA < valueB) return sortDirection === 'ASC' ? -1 : 1;
        if (valueA > valueB) return sortDirection === 'ASC' ? 1 : -1;
      }
      return 0;
    });
  }

  override onPostLoadData(records: any[]): void {
    this.getVGridContext().broadcastDataLoad(records);
    if (!this.getVGridContext().listeners['sort']) this.sort(records);
  }

  onInputChange = (ctx: grid.VGridContext, dRec: grid.DisplayRecord, _field: grid.FieldConfig, oldVal: any, newVal: any) => {
    if (oldVal !== newVal) {
      dRec.getRecordState().markModified();
      ctx.getVGrid().forceUpdateView();
    }
  }

  createVGridConfig() {
    let { appContext, pageContext, type, plugin, listConfig } = this.props;
    let pluginImpl = plugin as UIJobTrackingListPlugin;
    listConfig.buildFields(this);

    let writeCap = pageContext.hasUserWriteCapability();
    let adminCap = pageContext.hasUserAdminCapability();
    let permissionHelper = listConfig.permissionHelper;
    let configId = `job-tracking:${listConfig.project.code} `;
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 30,
        computeDataRowHeight: (context: grid.VGridContext, dRec: grid.DisplayRecord) => {
          let rec = dRec.record;
          if (rec.rowHeight > 0) return rec.rowHeight;
          return 30;
        },
        onchangeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord, deltaY: number) => {
          let rec = dRec.record;
          let currHeight = rec.rowHeight;
          if (currHeight < 10) currHeight = grid.DEFAULT.row.height;
          rec.rowHeight = currHeight + deltaY;
          dRec.getRecordState().markModified();
          this.vgridContext.getVGrid().forceUpdateView();
          return true;
        },
        editor: {
          supportViewMode: ['table', "aggregation"],
          enable: true
        },
        control: {
          width: 50,
          items: [
            {
              name: 'copy', hint: 'Copy Row', icon: FeatherIcon.Copy,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                const onClick = () => {
                  this.onCopyAction(dRecord);
                }
                return (
                  <bs.Button laf='link' className={`px-1`} onClick={onClick}>
                    <bs.Tooltip tooltip='Copy'>
                      <FeatherIcon.Copy size={12} />
                    </bs.Tooltip>
                  </bs.Button>
                );
              },
            },
            {
              name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let record = dRecord.record;
                if (!permissionHelper.canDeleteRow(record)) return;
                if (record.ownerAccountId !== app.host.DATATP_SESSION.getAccountId() && !adminCap && !permissionHelper.hasModeratorPermission()) {
                  return <></>
                }
                const onClick = () => {
                  ctx.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRecord.row);
                  ctx.getVGrid().forceUpdateView();
                }
                return (
                  <bs.Button laf='link' className={`px-1`} onClick={onClick}>
                    <bs.Tooltip tooltip='Delete'>
                      <FeatherIcon.Trash size={12} />
                    </bs.Tooltip>
                  </bs.Button>
                );
              }
            },
          ]
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'ownerAccountFullName', label: 'Owner', state: { showRecordState: true }, width: 250,
            filterable: true, filterableType: 'options',
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let record = dRecord.record;
              let cssClass = ''
              if (field.computeCssClasses) cssClass = field.computeCssClasses(ctx, dRecord);
              return (
                <div className={`flex-hbox justify-content-center align-items-center ${cssClass}`} >
                  <module.account.WAvatars className='px-2 text-info'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record.ownerAccountId]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className={`flex-hbox justify-content-start`} >{record.ownerAccountFullName}</div>
                </div >
              )
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (cell.getRow() === event.row) {
                  cell.forceUpdate();
                }
              }
            },
            onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let uiRoot = ctx.uiRoot;
              let uiList = uiRoot as UIJobTrackingList;
              uiList.onSelect(dRecord);
            },
          },
          ...listConfig.fields,
          { ...renderSummaryOfStepsConfig(listConfig) }
        ],
        fieldGroups: listConfig.fieldGroups
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap, {
            name: 'export-xlsx', label: 'Export', hint: 'Export XLSX', icon: FeatherIcon.Download,
            createComponent: function (ctx: grid.VGridContext) {
              let html = (<XLSXButton appContext={appContext} pageContext={pageContext} context={ctx} />)
              return html;
            }
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, "Add"),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, "Del"),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], type == 'selector'),
          module.settings.createVGridFavButton(configId),
        ],

        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-job-tracking-list', T('Refresh')),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true, pluginImpl.view === 'claim' ? 'search' : 'filter'),
      },
      footer: {
        default: {
          render: (ctx: grid.VGridContext) => {
            return (<UIJobTrackingListFooter context={ctx} />);
          }
        }
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 200,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel("All", false);
              return model;
            }
          }
        }
      }
    };

    if (listConfig.getGroupByColumns().length > 0) {
      let aggModel = new grid.AggregationDisplayModel(T('All'), false);
      if (permissionHelper.hasAllRowPermissions()) {
        let agg = new grid.ValueAggregation(T("Owner"), "ownerAccountId", true).withFieldGetter(rec => rec['ownerAccountFullName']);
        aggModel.addAggregation(agg);
      }

      let groupByColumns = listConfig.getAccessColumns();
      for (let column of groupByColumns) {
        let agg: any;
        if (column.dataType === CellDataType.Values && column.dataInputType === CellDataInputType.Date) {
          agg = new grid.DateValueAggregation(T(column.label), column.name, 'YYYY-MM-DD', false)
        } else {
          agg = new grid.ValueAggregation(T(column.label), column.name, false);
        }
        aggModel.addAggregation(agg);
      }
      config.view.availables['aggregation'] = {
        viewMode: 'aggregation', treeWidth: 250,
        createAggregationModel(_ctx: grid.VGridContext) {
          for (let agg of aggModel.aggregations) {
            let sumFunc = agg.aggFunctions.find(sel => sel.name === 'Total');
            if (!sumFunc) {
              sumFunc = new grid.SumAggregationFunction(`Total`, [...listConfig.getNumberedColumns(), 'stepDoneCount'], true);
              agg.withAggFunction(sumFunc);
            }
            agg.withOnClick((bucket: grid.Bucket) => {
              let collapse = bucket.collapse;
              bucket.collapse = !collapse;
              _ctx.model.getDisplayRecordList().updateDisplayRecords();
              _ctx.vgrid?.forceUpdateView();
            });
          }
          return aggModel;
        }
      }
    }
    return config;
  }

  onCopyAction(dRecord: grid.DisplayRecord) {
    let { listConfig, plugin } = this.props;
    let newRecord = dRecord.cloneRecord();
    delete newRecord['jobTrackingId'];
    delete newRecord['ownerAccountFullName'];
    delete newRecord['ownerAccountId'];
    delete newRecord['stepDoneCount'];
    delete newRecord['lastStepName'];
    for (let plugin of listConfig.project.plugins) {
      for (let column of plugin.columns) {
        if (column.dataType === CellDataType.Status) {
          newRecord[column.name] = {
            status: StepStatus.NA,
          }
        }
      }
    }
    plugin.getListModel().insertDisplayRecordAt(dRecord.row, newRecord);
    let state = grid.getRecordState(newRecord);
    state.markModified();
    this.vgridContext.getVGrid().forceUpdateView();
  }

  onNewAction = () => {
    let { plugin, listConfig } = this.props;
    let newRecord = {
      jobTrackingProjectId: listConfig.project.id
    }
    this.onAddRecord(newRecord, (rec) => {
      plugin.getRecords().unshift(rec);
      grid.initRecordStates([rec]);
      plugin.getListModel().filter();

      let state = grid.getRecordState(rec);
      state.markModified();
      this.vgridContext.getVGrid().forceUpdateView();
    });
  }

  onAddRecord(record: any, _callback: (rec: any) => void) {
    let { appContext } = this.props;
    let callBack = (data: any) => {
      _callback(data);
    }
    appContext
      .createHttpBackendCall('JobTrackingService', 'newJobTrackingModel', { model: record })
      .withSuccessData(callBack)
      .call();
  }

  onDeleteAction = () => {
    let { plugin, listConfig } = this.props;
    let permissionHelper = listConfig.permissionHelper;
    if (!permissionHelper.canDeleteAllRow(plugin.getListModel().getSelectedRecords())) {
      let message = "You don't have permission";
      bs.notificationShow("danger", message, <div className='alert alert-danger'>{message}</div>);
      return;
    }
    let selectedDRecs: Array<grid.DisplayRecord> = plugin.getListModel().getSelectedDisplayRecords();
    for (let dRec of selectedDRecs) {
      this.vgridContext.model.getDisplayRecordList().toggleDeletedDisplayRecord(dRec.row);
      this.vgridContext.getVGrid().forceUpdateView();
    }
  }

  onSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext, onSelect } = this.props;
    if (onSelect) {
      onSelect(appContext, pageContext, dRecord.record);
      return;
    }
    this.onDefaultSelect(dRecord);
  }
}

export class UIJobTrackingListFooter extends Component<grid.VGridContextProps> {
  saving = false;

  onSaveRows = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, onModifyBean } = uiRoot.props;
    let selectedRecords = context.model.getSelectedRecords();
    if (selectedRecords.length > 0) {
      for (let rec of selectedRecords) {
        let recordState = grid.getRecordState(rec);
        if (!rec['_state']) rec['_state'] = new grid.RecordState(recordState.row);
      }
    }

    let modifiedRecords = context.model.getModifiedRecords();
    if (modifiedRecords.length == 0) {
      context.getVGrid().forceUpdateView();
      appContext.addOSNotification("success", `Save Success`);
      return;
    }

    let records: Array<any> = [];
    for (let i = 0; i < modifiedRecords.length; i++) {
      let record = modifiedRecords[i];
      let recordState = grid.getRecordState(record);
      if (recordState.isMarkDeleted()) {
        record.editState = 'DELETED';
      } else {
        record.editState = 'MODIFIED';
      }
      record['uikey'] = `uikey-${util.IDTracker.next()}`;
      records.push(record);
    }

    let successCB = (data: any) => {
      if (onModifyBean) onModifyBean(data);
      let jobTrackingDbs = data;
      let jobTrackingIds = [];
      for (let jobTrackingDb of jobTrackingDbs) {
        let jobTrackingId = jobTrackingDb['jobTrackingId'];
        let uikey = jobTrackingDb['uikey'];
        let jobTracking = modifiedRecords.find(sel => sel['uikey'] === uikey);
        jobTracking['jobTrackingId'] = jobTrackingId;
        jobTrackingIds.push(jobTrackingId);
      }

      for (let modifiedRecord of modifiedRecords) {
        let recordState = grid.getRecordState(modifiedRecord);
        if (recordState.isMarkDeleted()) {
          context.model.removeRecord(modifiedRecord);
        }
      }
      this.onUpdateTrackingRows(jobTrackingIds, modifiedRecords);
    }

    appContext
      .createHttpBackendCall('JobTrackingService', 'processModels', { models: records })
      .withSuccessData(successCB)
      .call();
    this.saving = true;
    this.forceUpdate();

  }

  onUpdateTrackingRows = (jobTrackingIds: Array<number>, modifiedRecords: Array<any>) => {
    let { context, } = this.props;
    let uiRoot = context.uiRoot as UIJobTrackingList;
    let { appContext, listConfig } = uiRoot.props;
    let project = listConfig.project;

    if (jobTrackingIds.length == 0) {
      context.getVGrid().forceUpdateView();
      appContext.addOSNotification("success", `Save Success`);
      this.saving = false;
      this.forceUpdate();
      return;
    }

    let successCB = (data: any) => {
      let jobTrackingDbs = data;
      for (let sel of jobTrackingDbs) {
        let jobTrackingId = sel['jobTrackingId'];
        let modifiedRecord = modifiedRecords.find(rec => rec['jobTrackingId'] === jobTrackingId);
        for (let property in sel) {
          modifiedRecord[property] = sel[property];
        }
        let recordState = grid.getRecordState(modifiedRecord);
        modifiedRecord['_state'] = new grid.RecordState(recordState.row);
      }
      context.getVGrid().forceUpdateView();
      appContext.addOSNotification("success", `Save Success`);
      this.saving = false;
      this.forceUpdate();
    };
    let params = {
      params: {
        'jobTrackingProjectId': project['id'],
        'jobTrackingIds': jobTrackingIds
      }
    }
    appContext
      .createHttpBackendCall('JobTrackingService', 'searchJobTrackings', { params: params })
      .withSuccessData(successCB)
      .call();
  }

  onResetRowState = () => {
    let { context } = this.props;
    let records = context.model.getFilterRecords();
    for (let record of records) {
      grid.initRecordState(record, record['row'])
    }
    context.getVGrid().forceUpdateView();
  }

  onReport = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { pageContext } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let createAppPage = (appContext: app.AppContext, pageContext: app.PageContext) => {
      return (
        <UIReportForm appContext={appContext} pageContext={pageContext}
          context={context} observer={new entity.BeanObserver({})} readOnly={!writeCap} />
      );
    }
    pageContext.createPopupPage('job-tracking-report-form', T('Report'), createAppPage, { size: 'sm', backdrop: 'static' });
  }

  onShowIssue = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIJobTrackingList;
    let { pageContext, listConfig } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let createAppPage = (appContext: app.AppContext, pageContext: app.PageContext) => {
      return (
        <UIJobTrackingIssueList appContext={appContext} pageContext={pageContext} readOnly={!writeCap} viewTable='aggregation'
          plugin={
            new UIJobTrackingIssuePlugin()
              .withDataScope(appContext.getUserDataScope())
              .withJobTrackingProjectId(listConfig.project.id)
          } />
      );
    }
    pageContext.createPopupPage('job-tracking-issue', T('Issue Report'), createAppPage, { size: 'lg', backdrop: 'static' });
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIJobTrackingList;
    let { appContext, pageContext, type, listConfig } = uiRoot.props;
    let permissionHelper: JobTrackingPermissionHelper = listConfig.permissionHelper;
    return (
      <bs.Toolbar>
        <entity.WButtonEntityWrite hide={type === 'selector'}
          appContext={appContext} pageContext={pageContext}
          icon={FeatherIcon.Book}
          label={T('Issue Report')} onClick={this.onShowIssue} />
        <entity.WButtonEntityWrite hide={type === 'selector'}
          appContext={appContext} pageContext={pageContext}
          icon={FeatherIcon.Book}
          label={T('Report')} onClick={this.onReport} />
        <entity.WButtonEntityWrite hide={type === 'selector' || !permissionHelper.hasPermission(app.WRITE)}
          appContext={appContext} pageContext={pageContext}
          label={T('Reset')} onClick={this.onResetRowState} />
        {type === 'selector' || !permissionHelper.hasPermission(app.WRITE) ? null :
          <bs.Button laf='primary' disabled={this.saving} onClick={this.onSaveRows}>
            {this.saving ?
              <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />
              :
              <FeatherIcon.Save size={12} />
            }
            <span style={{ marginLeft: 4 }}>{T('Save Changes')}</span>
          </bs.Button>
        }
      </bs.Toolbar>
    );
  }
}
