import React from 'react';
import { app, grid, bs, entity, sql, util, input } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { app as ieApp } from '@datatp-ui/document-ie';
import { T } from '../backend';
import * as FeatherIcon from 'react-feather';

const SESSION = app.host.DATATP_HOST.session;

import { TMSLOLOInvoiceReconcileAccountingStatus, TMSLOLOInvoiceReconcileStatus } from '../models';
import { BBRefVehicleFleet } from '../vehicle/BBRefVehicleFleet';
import { vendorbill } from 'app/logistics';

export class UIInvoiceReconcileListPlugin extends entity.DbEntityListPlugin {
  needConfirm = false;
  constructor(type: string, dateRange?: util.TimeRange) {
    super([]);
    this.backend = {
      context: 'company',
      service: 'InvReconcileDocumentService',
      searchMethod: 'searchInvoiceSummaryReconciles',
      changeStorageStateMethod: 'changeInvoiceSummaryReconcileStorageState',
      deleteMethod: 'deleteInvoiceSummaryReconciles'
    }

    this.searchParams = {
      "params": { type: type },
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "rangeFilters": [
        ...sql.createDateTimeFilter("paymentRequestDate", "Payment Request Date", dateRange),
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 3000,
    }
  }

  withDataScope = (dataScope?: app.AppDataScope) => {
    this.addSearchParam("dataScope", dataScope?.scope);
    return this;
  }

  withNeedConfirm() {
    this.addSearchParam("needConfirm", true);
    this.needConfirm = true;
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }

  withMaxReturn(maxReturn: number) {
    if (this.searchParams) this.searchParams.maxReturn = maxReturn;
    return this;
  }
}

interface UITMSLOLOInvoiceReconcileListProps extends entity.DbEntityListProps {
  invoice_type: string;
  viewByVendor?: boolean;
}

export class UITMSLOLOInvoiceReconcileList extends entity.DbEntityList<UITMSLOLOInvoiceReconcileListProps> {
  createVGridConfig() {
    let { type, appContext, pageContext, readOnly, plugin, viewByVendor, onModifyBean } = this.props;
    let pluginImpl = plugin as UIInvoiceReconcileListPlugin;
    let title = 'TMS LOLO';
    let needConfirm = pluginImpl.needConfirm;
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let dRecord = fieldCtx.displayRecord;
      let field = fieldCtx.fieldConfig;
      let ctx = fieldCtx.gridContext;
      let event: grid.VGridCellEvent = {
        row: dRecord.row, field: field, event: 'Modified', data: dRecord
      }
      ctx.broadcastCellEvent(event);
    }
    let dataScope = plugin.getSearchParams().params['dataScope'];
    const modeCap = !readOnly && pageContext.hasUserModeratorCapability();
    const writeCap = !readOnly && pageContext.hasUserWriteCapability();
    let picCustomRender = (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      let lolo = dRecord.record;
      let value = lolo['nickname'];
      if (!value) {
        if (lolo.fullName) {
          value = lolo['fullName'];
        } else {
          value = lolo['responsibleFullName'];
        }
      }
      return (
        <div className='flex-hbox justify-content-center align-items-center' >
          <module.account.WAvatars className='px-2'
            appContext={appContext} pageContext={pageContext} avatarIds={[lolo['responsibleAccountId']]}
            avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
          <bs.Tooltip tooltip={lolo['responsibleFullName']} className="flex-hbox">
            {value}
          </bs.Tooltip>
        </div>
      )
    }

    let config: grid.VGridConfig = {
      title: T(title),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          {
            ...entity.DbEntityListConfigTool.FIELD_INDEX(), state: { showRecordState: true }, width: 55,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow()) {
                  cell.forceUpdate();
                }
              },
            },
          },
          { name: 'code', label: T('Code'), width: 150, container: 'fixed-left' },
          {
            name: 'vendorName', label: 'Thầu Phụ', hint: T('Vendor'), width: 200, cssClass: 'px-1', filterableType: 'options', filterable: true,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                let cssClass = field.cssClass;
                const oldVal = lolo[field.name];
                return (
                  <BBRefVehicleFleet minWidth={400}
                    className={cssClass} appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus}
                    bean={lolo} beanIdField={'vendorId'} beanLabelField={'vendorName'} placeholder={'Vendor'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
                      onInputChange(bean, field.name, oldVal, bean[field.name])
                    }}
                  />
                )
              },
            }
          },
          {
            name: 'total', label: 'Số Tiền', hint: T('Total'), editor: { type: 'currency' }
          },
          {
            name: 'currency', label: T('Currency'), width: 80,
            state: { visible: false },
            editor: {
              type: "string",
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { tabIndex, focus, fieldConfig, displayRecord } = ctx;
                let lolo = displayRecord.record;
                const oldVal = lolo['currency'];
                return (
                  <module.settings.BBRefCurrency tabIndex={tabIndex} autofocus={focus}
                    appContext={appContext} pageContext={pageContext} required hideMoreInfo placeholder='Currency'
                    bean={lolo} beanIdField={fieldConfig.name} onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => {
                      onInputChange(bean, fieldConfig.name, oldVal, selectOpt.name)
                    }} />
                )
              },
              onInputChange: onInputChange
            },
          },
          {
            name: 'paymentRequestDate', label: 'Ngày Nhận', hint: T('Payment Request Date'), width: 150, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass}
                    bean={lolo} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    timeFormat={true}
                    onInputChange={onInputChange} />
                );
              },
            }
          },
          {
            name: 'responsibleFullName',
            label: T('PIC'),
            filterableType: 'options',
            filterable: true,
            width: 200,
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let ctx = fieldCtx.gridContext;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                const oldVal = lolo['responsibleAccountId'];
                if (dataScope === app.AppDataScope.OWNER.scope) {
                  return picCustomRender(ctx, field, dRecord, focus);
                }
                let disable = !writeCap;
                if (viewByVendor) disable = true;
                return (
                  <module.account.BBRefAccount minWidth={600} tabIndex={tabIndex} autofocus={focus}
                    key={`account-${util.IDTracker.next()}`}
                    appContext={appContext} pageContext={pageContext} disable={disable}
                    placeholder='Enter An Account'
                    bean={lolo} accountIdField={'responsibleAccountId'} accountLabelField={'responsibleFullName'}
                    onPostUpdate={(_inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      let newVal = bean['responsibleAccountId'];
                      if (newVal && oldVal != newVal) {
                        bean['handoverDate'] = util.TimeUtil.toCompactDateTimeFormat(new Date());
                        bean['status'] = bean['status'] == TMSLOLOInvoiceReconcileStatus.NEW ? TMSLOLOInvoiceReconcileStatus.HANDED_OVER : bean['status'];
                      }
                      onInputChange(bean, 'responsibleAccountId', oldVal, newVal)
                    }}
                  />
                )
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'confirm') {
                  cell.forceUpdate();
                }
              },
            },
            customRender: picCustomRender
          },
          {
            name: 'handoverDate', label: 'Ngày Ban Giao', hint: T('Handover Date'), width: 150, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass} key={util.IDTracker.next()}
                    bean={lolo} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY' timeFormat={true}
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'responsibleFullName') {
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'status', label: T('Status'), width: 150,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'responsibleFullName') {
                  cell.forceUpdate();
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                return (
                  <input.BBSelectField
                    tabIndex={tabIndex} focus={focus}
                    bean={lolo} field={field.name}
                    options={[TMSLOLOInvoiceReconcileStatus.NEW, TMSLOLOInvoiceReconcileStatus.HANDED_OVER, TMSLOLOInvoiceReconcileStatus.PENDING, TMSLOLOInvoiceReconcileStatus.COMPLETED]}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      if (oldVal !== newVal && newVal === TMSLOLOInvoiceReconcileStatus.COMPLETED) {
                        lolo['completionDate'] = util.TimeUtil.toCompactDateTimeFormat(new Date());
                      }
                      onInputChange(bean, field, oldVal, newVal);
                    }} />
                )
              },
            }
          },
          {
            name: 'completionDate', label: 'Ngày Xong', hint: T('Completion Date'), width: 150, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass} key={util.IDTracker.next()}
                    bean={lolo} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    timeFormat={true}
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'status') {
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'closeDate', label: 'Ngày Đóng', hint: T('Close Date'), width: 150, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
            state: { visible: false },
            editor: {
              type: 'date',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBDateInputMask className={cssClass} key={util.IDTracker.next()}
                    bean={lolo} field={field.name} tabIndex={tabIndex} focus={focus} format='DD/MM/YYYY'
                    timeFormat={true}
                    onInputChange={onInputChange} />
                );
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'accountingStatus') {
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'description', label: 'Ghi Chú', hint: T('Description'), width: 250, editor: { type: 'text', onInputChange: onInputChange },
          },
          {
            name: 'accountingStatus', label: T('Accounting Status'), width: 150,
            state: { visible: false },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let dRecord = fieldCtx.displayRecord;
                let field = fieldCtx.fieldConfig;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let lolo = dRecord.record;
                return (
                  <input.BBSelectField
                    tabIndex={tabIndex} focus={focus}
                    bean={lolo} field={field.name}
                    options={[TMSLOLOInvoiceReconcileAccountingStatus.NONE, TMSLOLOInvoiceReconcileAccountingStatus.NEED_REVIEW, TMSLOLOInvoiceReconcileAccountingStatus.FINISH]}
                    onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                      if (oldVal !== newVal && newVal === TMSLOLOInvoiceReconcileAccountingStatus.FINISH) {
                        lolo['closeDate'] = util.TimeUtil.toCompactDateTimeFormat(new Date());
                      }
                      onInputChange(bean, field, oldVal, newVal);
                    }} />
                )
              },
            }
          },
          { name: 'label', label: 'Nhãn', editor: { type: 'string', onInputChange: onInputChange }, },
          {
            name: 'messageId', label: '', width: 50, customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let fieldCtx: grid.FieldContext = {
                fieldConfig: field, displayRecord: dRecord, gridContext: ctx, focus: false, tabIndex: 0
              }
              let record = dRecord.record;
              let color = 'orange';
              const oldVal = record['messageId'];
              if (record['messageId']) {
                color = 'green';
              }
              let successData = (data: any) => {
                let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                  return <module.communication.message.UIMessageEditor
                    appContext={appCtx} pageContext={pageCtx} onPostCommit={(message: any) => {
                      pageCtx.back();
                      appContext
                        .createHttpBackendCall('InvReconcileDocumentService', 'updateInvoiceReconcileMessageId', { loloId: record.id, messageId: message.id })
                        .withSuccessData((data: any) => {
                          record['messageId'] = message.id;
                          onInputChange(fieldCtx, oldVal, message.id);
                        })
                        .call();
                    }}
                    observer={new entity.ComplexBeanObserver(data)} />
                }
                pageContext.createPopupPage('message', T('Message'), createAppPage, { size: "lg", backdrop: "static" });
              }
              let onClick = () => {
                appContext.createHttpBackendCall('InvReconcileDocumentService', 'createInvoiceReconcileMessage', { loloId: record.id }).withSuccessData(successData).call();
              }
              return (<div className="flex-hbox justify-content-center" ><FeatherIcon.Mail size={15} color={color} onClick={onClick} /></div>);
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'messageId') {
                  cell.forceUpdate();
                }
              },
            },
          },
          { name: 'paymentOverdue', label: 'Trễ hạn thanh toán', hint: T('Trễ hạn thanh toán') },
          {
            name: 'delayedPaymentTime', label: 'Thời gian trễ hạn thanh toán', hint: T('Thời gian trễ hạn thanh toán (giờ)'),
            fieldDataGetter: (record: any) => {
              let seconds = record['delayedPaymentTime'];
              let hours = seconds / 3600;
              return Number(hours.toFixed(2));
            },
          },
          {
            name: 'actions', label: 'Bộ Thanh Toán', hint: T('Actions'), width: 450,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let record = dRecord.record;
              let isNew = !record.id ? true : false;
              let fieldCtx: grid.FieldContext = {
                fieldConfig: field,
                gridContext: ctx,
                displayRecord: dRecord,
                focus: focus,
                tabIndex: 0,
              }

              return (
                <div className='flex-hbox'>
                  <bs.Button laf='link' disabled={isNew} onClick={() => this.onAddDocumentSet(fieldCtx, onInputChange)}>
                    <FeatherIcon.Plus size={12} />
                  </bs.Button>
                  {this.onBuildDocumentSets(fieldCtx, onInputChange)}
                </div>
              )
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'actions') {
                  cell.forceUpdate();
                }
              },
            },
          },
          {
            name: 'confirm', label: (T('Confirm')), container: 'fixed-right',
            customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
              let record = dRecord.record;
              let disabled = true;
              if (writeCap && !viewByVendor && !record['responsibleAccountId']) {
                disabled = false;
              }
              let onClick = () => {
                appContext
                  .createHttpBackendCall('InvReconcileDocumentService', 'updatePICInvoiceSummaryReconciles', { id: dRecord.record['id'] })
                  .withSuccessData((data: any) => {
                    let accountId = SESSION.getAccountAcl().getAccountId();
                    let fullName = SESSION.getAccountAcl().getFullName();

                    record['responsibleAccountId'] = accountId;
                    record['responsibleFullName'] = fullName;
                    let fieldCtx: grid.FieldContext = {
                      displayRecord: dRecord,
                      fieldConfig: field,
                      gridContext: ctx,
                      tabIndex: 0,
                      focus: focus
                    }
                    onInputChange(fieldCtx, null, record['responsibleAccountId'])
                    if (onModifyBean) {
                      onModifyBean(record)
                    }
                  })
                  .withSuccessNotification('success', T('Success'))
                  .call()
              }
              return (
                <bs.Button disabled={disabled} laf='success' className='m-1 p-1 flex-hbox-grow-0 align-items-center justify-content-center' style={{ height: 27, width: 100, fontSize: 12 }} outline
                  onClick={onClick} >
                  {T('Confirm')} <FeatherIcon.Check size={10} />
                </bs.Button>
              )
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row == cell.getRow() && event.field.name === 'confirm') {
                  cell.forceUpdate();
                }
              },
            },
          },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ],
        computeDataRowHeight: (_ctx: grid.VGridContext, dRec: grid.DisplayRecord) => {
          return 35;
        },
        control: {
          width: 25,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let record = dRecord.record;
                record = {
                  ...record,
                  id: null,
                  label: '/',
                  status: TMSLOLOInvoiceReconcileStatus.NEW,
                  accountingStatus: TMSLOLOInvoiceReconcileAccountingStatus.NONE,
                  completionDate: null,
                  closeDate: null,
                };
                ctx.model.insertDisplayRecordAt(dRecord.row, record);
                grid.getRecordState(record).markModified(true);
                ctx.getVGrid().forceUpdateView();
              },
            },
          ]
        },
        editor: {
          supportViewMode: ['table'],
          enable: true
        },

      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(needConfirm || !writeCap, T('Add')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], needConfirm || !writeCap),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(needConfirm || !modeCap, T('Del')),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(plugin.searchParams ? true : false, 'filter'),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('refresh', T('Refresh')),
        ],
      },
      footer: {
        page: {
          hide: !writeCap,
          render: (ctx: grid.VGridContext) => {
            return (
              <bs.Toolbar className='border' >
                {!viewByVendor ? <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.List}
                  disable={needConfirm || !writeCap}
                  label={T("Need Confirm")} onClick={() => this.onShowInvoiceReconcileNeedConfirm()} />
                  : <></>
                }
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.ArrowRightCircle}
                  disable={needConfirm || !writeCap}
                  label={T("Documents")} onClick={this.onShowDocuments} />
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save}
                  disable={needConfirm || !writeCap}
                  label={T("Save")} onClick={this.onSave} />
              </bs.Toolbar>
            )
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T("Select"), type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    if (!pageContext.hasUserWriteCapability() || readOnly) delete config.record.editor;
    return config;
  }

  onShowInvoiceReconcileNeedConfirm() {
    let { pageContext, invoice_type, plugin } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSLOLOInvoiceReconcileList plugin={new UIInvoiceReconcileListPlugin(invoice_type).withNeedConfirm()}
          invoice_type={invoice_type} onModifyBean={(bean: any) => plugin.addOrUpdateByRecordId(this, bean)}
          appContext={appCtx} pageContext={pageCtx} />
      );
    }
    pageContext.createPopupPage('invoice-reconcile-need-confirm', `Need Confirm`, createAppPage, { size: 'xl' });
  }

  onShowDocuments = () => {
    let { pageContext } = this.props;
    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    let companyCode = company.companyCode;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <ieApp.document.DocumentList key={'documents'}
          appContext={appCtx} pageContext={pageCtx} viewType="DocumentSet" viewName="aggregation"
          storage={new module.storage.CompanyStorage(companyCode)} plugin={new ieApp.document.DocumentListPlugin()} />
      );
    }
    pageContext.addPageContent("document-set", T("Document Set"), createAppPage);

  }

  onNewAction() {
    let { plugin, invoice_type, viewByVendor, appContext } = this.props;
    let dataScope = plugin.getSearchParams().params['dataScope'];
    let currentDate = util.TimeUtil.toCompactDateTimeFormat(new Date());
    let accountId: any = SESSION.getAccountAcl().getAccountId();
    let fullName: any = SESSION.getAccountAcl().getFullName();
    let newRecord = {
      label: '/',
      responsibleAccountId: accountId,
      responsibleFullName: fullName,
      handoverDate: currentDate,
      status: TMSLOLOInvoiceReconcileStatus.NEW,
      accountingStatus: TMSLOLOInvoiceReconcileAccountingStatus.NONE,
      paymentRequestDate: currentDate,
      currency: 'VND',
      type: invoice_type,
      vendorId: null,
      vendorName: null
    }
    if (viewByVendor) {
      newRecord = {
        ...newRecord,
        responsibleAccountId: null,
        responsibleFullName: null,
      }
      appContext
        .createHttpBackendCall('VehicleFleetService', 'getVehicleFleetByOwnerAccountId', { ownerAccountId: accountId })
        .withSuccessData((data: any) => {
          newRecord = {
            ...newRecord,
            vendorId: data.id,
            vendorName: data.label
          }
          let records = plugin.getListModel().getRecords();
          records.unshift(newRecord);
          plugin.update(records);
          grid.getRecordState(newRecord).markModified();
          this.getVGridContext().getVGrid().forceUpdateView();
        })
        .call();

    } else {
      let records = plugin.getListModel().getRecords();
      records.unshift(newRecord);
      plugin.update(records);
      grid.getRecordState(newRecord).markModified();
      this.getVGridContext().getVGrid().forceUpdateView();
    }
  }

  onShowDocumentSet = (docSet: any) => {
    let { pageContext } = this.props;
    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    let companyCode = company.companyCode;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <ieApp.document.DocumentSet
          appContext={appCtx} pageContext={pageCtx}
          observer={new entity.BeanObserver(docSet)} storage={new module.storage.CompanyStorage(companyCode)} />
      );
    }
    pageContext.addPageContent("document-set", T("Document Set"), createAppPage);
  }

  onShowDocSet = (docSets: Array<any>, docSetId: number) => {
    let { pageContext } = this.props;

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let docSetTabs: Array<any> = [];
      docSets.forEach(docSet => {
        docSetTabs.push(
          <bs.Tab label={docSet['name']} name={`doc-set-${docSet['id']}`} active={docSet['id'] === docSetId}>
            <UIDocumentSetLoadable key={`doc-set-${docSet['id']}`} appContext={appCtx} pageContext={pageCtx} documentSetId={docSet['id']} />
          </bs.Tab>
        )
      })
      return (
        <bs.TabPane>
          {docSetTabs}
        </bs.TabPane>
      );
    }
    pageContext.addPageContent("document-set", T("Document Set"), createAppPage);
  }

  onBuildDocumentSets = (fieldCtx: grid.FieldContext, onInputChange: (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => void) => {
    let { appContext } = this.props;
    let record = fieldCtx.displayRecord.record;
    let loloId = record['id'];
    // let type = record['type'];
    let docSets: Array<any> = record['documentSets'];
    if (!docSets || docSets.length == 0) return;
    let docSetBtns: Array<any> = [];
    let onDelete = (docSet: any) => {
      let onConfirm = () => {
        appContext
          .createHttpBackendCall('InvReconcileDocumentService', 'deleteInvoiceReconcileDocument', { docSetId: docSet['id'], loloId: loloId })
          .withSuccessData((data: any) => {
            let oldDocSets = [...docSets];
            let index = docSets.indexOf(docSet);
            docSets.splice(index, 1);
            onInputChange(fieldCtx, oldDocSets, docSets);
          })
          .withSuccessNotification('success', T('Remove Success'))
          .call();
      }

      bs.dialogConfirmMessage('Confirm', T(`Are you sure you want to unlink this document set ?`), onConfirm)
    }
    for (let docSet of docSets) {
      docSetBtns.push(
        <div className='mx-1'>
          <bs.Button key={docSet.documentSetId} laf='info' className='px-1 py-0 m-0' onClick={() => this.onShowDocSet(docSets, docSet['id'])}>
            {docSet.name}
          </bs.Button>
          <FeatherIcon.Trash2 className='text-danger' style={{ cursor: 'pointer' }} size={14} onClick={() => onDelete(docSet)} />
        </div>
      )
    }
    return docSetBtns;
  }

  onAddDocumentSet = (fieldCtx: grid.FieldContext, onInputChange: (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => void) => {
    let { appContext, pageContext, invoice_type } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let record = fieldCtx.displayRecord.record;

    // let documentCategoryIds = { FCL: 5, LCL: 4 };

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      // let documentCategoryId = documentCategoryIds[invoice_type as keyof typeof documentCategoryIds] || 1;
      let newDocumentSet = {
        name: record['label'] ? record['label'] : "/",
        // documentCategoryId: documentCategoryId,
        documentCategoryLabel: invoice_type,
        description: record['code'],
        type: 'tms-inv-summary',
      }
      let bean = {
        id: null,
        name: null
      }
      return (
        <bs.TabPane>
          <bs.Tab name="New" label='New' active={true}>
            <DocumentSet appContext={appCtx} pageContext={pageCtx} context={this.getVGridContext()} bean={newDocumentSet} lolo={record} />
          </bs.Tab>
          <bs.Tab name='link' label='Link'>
            <div className='flex-vbox'>
              <ieApp.document.BBRefDocumentSet label={T('Document Set')} minWidth={400} className='p-1'
                appContext={appCtx} pageContext={pageCtx} disable={!writeCap}
                bean={bean} beanIdField={'id'} beanLabelField={'name'} placeholder={'Document Set'}
              />
              <bs.Toolbar hide={!writeCap}>
                <entity.WButtonEntityWrite
                  appContext={appCtx} pageContext={pageCtx} icon={FeatherIcon.Link}
                  label={T('Link')}
                  onClick={() => {
                    let successData = (data: any) => {
                      appContext.addOSNotification('success', 'Link success');
                      let docSets: Array<any> = record.documentSets;
                      const oldDocSets = [...docSets];
                      docSets.push(bean);
                      onInputChange(fieldCtx, oldDocSets, docSets);
                      pageCtx.back();
                    }
                    this.onAddTMSLOLOInvoiceReconcileDocument(bean.id, record.id, successData)
                  }
                  }
                />

              </bs.Toolbar>
            </div>
          </bs.Tab>
        </bs.TabPane>

      );
    }
    pageContext.createPopupPage("document-set", T("Document Set"), createAppPage, { backdrop: "static" });
  }

  onAddTMSLOLOInvoiceReconcileDocument = (docSetId: any, loloId: any, successData: (data: any) => void) => {
    let { appContext } = this.props;
    appContext.createHttpBackendCall('InvReconcileDocumentService', 'createInvoiceReconcileDocument', {
      docSetId: docSetId,
      loloId: loloId,
    })
      .withSuccessData(successData)
      .call();
  }


  onSave = () => {
    let { appContext, plugin } = this.props;
    let records = plugin.getListModel().getMarkModifiedRecords();
    appContext
      .createHttpBackendCall('InvReconcileDocumentService', 'saveInvoiceSummaryReconciles', { lolos: records })
      .withSuccessData((data) => this.reloadData())
      .withSuccessNotification('success', T('Save Success'))
      .call();
  }
}

interface DocumentSetProps extends app.AppComponentProps {
  bean: any,
  lolo: any,
  context: grid.VGridContext,
}
class DocumentSet extends app.AppComponent<DocumentSetProps> {
  readOnly = false;

  render(): React.ReactNode {
    let { appContext, pageContext, context, lolo, bean } = this.props;
    let uiList = context.uiRoot as UITMSLOLOInvoiceReconcileList;

    return (
      <div className='flex-vbox'>
        <ieApp.document.BBRefDocumentSetCategory label={T('Category')} minWidth={400} className='p-1'
          appContext={appContext} pageContext={pageContext} disable={!pageContext.hasUserWriteCapability()}
          bean={bean} beanIdField={'documentCategoryId'} beanLabelField={'documentCategoryLabel'} placeholder={'Category'}
          onPostUpdate={(_inputUI: React.Component, bean: any, _selectOpt: any, _userInput: string) => {
            if (!bean['documentCategoryId']) {
              this.readOnly = true;
              bs.notificationShow("danger", T(`DocumentSetCategory must not be null!!!`));
            } else {
              if (this.readOnly) this.readOnly = false;
              bean['path'] = 'document/' + bean['documentCategoryId']
            }
            this.forceUpdate();
          }}
        />
        <ieApp.document.DocumentSetEditor
          appContext={appContext} pageContext={pageContext}
          readOnly={this.readOnly}
          observer={new entity.BeanObserver(bean)} onPostCommit={(docSet: any) => {
            pageContext.back();
            let successData = (data: any) => {
              if (!lolo['documentSets']) {
                lolo['documentSets'] = [docSet];
              } else {
                lolo['documentSets'].push(docSet);
              }
              context.getVGrid().forceUpdateView();
            }
            uiList.onAddTMSLOLOInvoiceReconcileDocument(docSet.id, lolo.id, successData);
            uiList.onShowDocumentSet(docSet);
          }} />
      </div>
    )
  }
}

interface UIDocumentSetLoadableProps extends app.AppComponentProps {
  documentSetId: number;
}
class UIDocumentSetLoadable extends app.AppComponent<UIDocumentSetLoadableProps> {
  documentSet: any;

  componentDidMount(): void {
    let { appContext, documentSetId } = this.props;
    appContext
      .createHttpBackendCall('DocumentService', 'getDocumentSet', { id: documentSetId })
      .withSuccessData((data: any) => {
        this.documentSet = data;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call();
  }

  render(): React.ReactNode {
    let { appContext, pageContext } = this.props;
    let company = app.host.DATATP_HOST.session.getAccountAcl().getCompanyAcl();
    let companyCode = company.companyCode;
    if (!this.documentSet) {
      return;
    }
    return (
      <ieApp.document.DocumentSet
        appContext={appContext} pageContext={pageContext}
        observer={new entity.BeanObserver(this.documentSet)} storage={new module.storage.CompanyStorage(companyCode)} />
    )
  }
}



