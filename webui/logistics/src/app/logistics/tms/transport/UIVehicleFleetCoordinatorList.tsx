import React, { Component } from 'react';
import * as FeatherIcon from 'react-feather'
import { app, server, util, grid, bs, sql, entity } from '@datatp-ui/lib';

import { T } from '../backend';
import { VehicleFleetURL } from '../RestURL';

import { UIVehicleFleetCoordinatorEditor } from './UIVehicleFleetCoordinator';

export class UICoordinatorListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'VehicleFleetService',
      searchMethod: 'searchVehicleFleetCoordinators',
      changeStorageStateMethod: 'changeVehicleFleetCoordinatorsStorageState'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "orderBy": {
        fields: ["label", "modifiedTime"],
        fieldLabels: [T("Label"), T("Modified Time")],
        selectFields: [],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  withVehicleFleet(code: string) {
    this.addSearchParam('fleetCode', code);
    return this;
  }

  withVehicleFleetId(id: number) {
    this.addSearchParam('vehicleFleetId', id);
    return this;
  }
}

interface UICoordinatorListProps extends entity.DbEntityListProps {
  vehicleFleetId?: number;
  vehicleFleetLabel?: string
}
export class UICoordinatorList extends entity.DbEntityList<UICoordinatorListProps> {
  createVGridConfig() {
    let { pageContext, type } = this.props;
    let moderatorCap = pageContext.hasUserModeratorCapability();
    let writeCap = pageContext.hasUserWriteCapability();
    let config: grid.VGridConfig = {
      title: 'Vehicle Fleets Coordinators',
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 220),
          { name: 'code', label: T('Code'), width: 200, state: { visible: false } },
          { name: 'fleetLabel', label: T('Fleet Label'), width: 200 },
          { name: 'accountFullName', label: T('Account Full Name'), width: 200, state: { visible: false } },
          { name: 'email', label: T('Email'), width: 250 },
          { name: 'mobile', label: T('Mobile'), width: 100 },
          { name: 'description', label: T('Description'), width: 200, state: { visible: false } },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ],
        summary: {
          dataCellHeight: 90,
          render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
            const infoField = ['fleetLabel', 'email', 'mobile'];
            let html = (
              <grid.SummaryCell className="flex-hbox" context={ctx} record={dRecord}>
                <grid.SummaryInfo context={ctx} record={dRecord} labelField="label" infoField={infoField}
                  descField="code" />
              </grid.SummaryCell>
            );
            return html;
          },
        },
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED], !moderatorCap),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!writeCap || type !== 'selector',
            {
              name: 'select-coordinator', label: T('Select'), icon: FeatherIcon.List,
              onClick: (ctx: grid.VGridContext) => {
                this.onVehicleFleetCoordinatorSelector();
              }
            }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap || type !== 'selector', T("Remove"))
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true)
      },
      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return (
              <UIVehicleFleetCoordinatorListPageControl context={ctx} />
            )
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T("Select"), type)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let coordinator = dRecord.record;
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();

    appContext.createHttpBackendCall('VehicleFleetService', 'getVehicleFleetCoordinator', { code: coordinator.code })
      .withSuccessData((data: any) => {
        let observer = new entity.ComplexBeanObserver(data);
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UIVehicleFleetCoordinatorEditor appContext={appCtx} pageContext={pageCtx} observer={observer} readOnly={!writeCap} onPostCommit={() => {
            this.reloadData();
            pageCtx.back();
          }
          } />
        }
        pageContext.createPopupPage('coordinator', T(`Coordinator: ${coordinator.code}`), createAppPage, { size: 'md', backdrop: 'static' });
      })
      .call();
  }

  onCoordinatorSelect = (appContext: app.AppContext, pageContext: app.PageContext, coordinator: any) => {
    let { onModifyBean, vehicleFleetId } = this.props;

    const membership = {
      vehicleFleetId: vehicleFleetId,
      coordinatorId: coordinator.id,
    }

    appContext.createHttpBackendCall('VehicleFleetService', 'addVehicleFleetMembership', { membership: membership })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification('success', T('Add Success'));
        if (onModifyBean) {
          onModifyBean(null, undefined);
        } else this.forceUpdate
        pageContext.back();
      })
      .withFailNotification("danger", T('Add Fail'))
      .call();
  }

  onCoordinatorMultiSelect = (appContext: app.AppContext, pageContext: app.PageContext, coordinators: Array<any>) => {
    coordinators.forEach(coordinator => {
      this.onCoordinatorSelect(appContext, pageContext, coordinator);
    })
  }

  override onDeleteAction() {
    let { appContext, plugin, vehicleFleetId } = this.props;
    let coordinators: Array<any> = plugin.getListModel().getSelectedRecords();

    if (coordinators.length === 0) {
      appContext.addOSNotification("warning", T('No Coordinator were Selected'));
    } else {
      let memberships: Array<any> = [];
      coordinators.forEach(coordinator => {
        let membership = {
          coordinatorId: coordinator.id,
          vehicleFleetId: vehicleFleetId,
        }
        memberships.push(membership);
      })

      appContext.createHttpBackendCall('TMSBillService', 'removeVehicleFleetMemberships', { memberships: memberships })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification('success', T('Remove Success'));
          plugin.getListModel().removeSelectedDisplayRecords();
          this.forceUpdate();
        })
        .withFailNotification('danger', T('Remove Fail'))
        .call();
    }
  }

  onVehicleFleetCoordinatorSelector() {
    let { appContext, pageContext, plugin } = this.props;
    let excludeRecordFilter = new entity.ExcludeRecordFilter(plugin.getListModel().getRecords(), 'code');
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return <UICoordinatorList
        appContext={appCtx} pageContext={pageCtx}
        onSelect={this.onCoordinatorSelect} type={'selector'}
        onMultiSelect={(appCtx, pageCtx, coordinators) => this.onCoordinatorMultiSelect(appCtx, pageCtx, coordinators)}
        plugin={new UICoordinatorListPlugin().withRecordFilter(excludeRecordFilter)} />
    }
    pageContext.createPopupPage('onVehicleFleetCoordinatorSelector', T('Coordinator'), createAppPage, { size: 'lg', backdrop: 'static' });

  }
}

export class UIVehicleFleetCoordinatorListPageControl extends Component<grid.VGridContextProps> {
  onNew() {
    const { context } = this.props;
    const uiRoot = context.uiRoot as UICoordinatorList;
    const { appContext, pageContext, vehicleFleetId, vehicleFleetLabel, onModifyBean } = uiRoot.props;
    const writeCap = pageContext.hasUserWriteCapability();

    let onPostCommit = (coordinator: any, _uiEditor?: app.AppComponent) => {
      if (!vehicleFleetId) uiRoot.reloadData();
      const membership = {
        vehicleFleetId: vehicleFleetId,
        coordinatorId: coordinator.id,
      }

      appContext.createHttpBackendCall('VehicleFleetService', 'addVehicleFleetMembership', { membership: membership })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T('Add Coordinator membership Success'))
          if (onModifyBean) {
            onModifyBean(null, undefined);
          } else this.forceUpdate();
          bs.dialogHideById(dialogId);
          uiRoot.reloadData();
        })
        .withFailNotification("danger", T('Add Coordinator membership Fail'))
        .call();
    }

    const vehicleFleetCoordinator = {
      code: `coordinator-${util.TimeUtil.toDateTimeIdFormat()}`,
      label: 'New Coordinator',
      fleetLabel: vehicleFleetLabel,
      storageState: entity.StorageState.ACTIVE,
      editMode: entity.EditMode.VALIDATED,
    }
    const html =
      <UIVehicleFleetCoordinatorEditor
        appContext={appContext} pageContext={pageContext} observer={new entity.ComplexBeanObserver(vehicleFleetCoordinator)}
        readOnly={!writeCap} onPostCommit={onPostCommit} />
    let dialogId = bs.dialogShow(T(`New Coordinator`), html)
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { appContext, pageContext } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <bs.Toolbar className='border' hide={!writeCap}>
        <entity.WButtonEntityNew
          appContext={appContext} pageContext={pageContext} label={T('Coordinator')} onClick={() => this.onNew()} />
      </bs.Toolbar>
    );
  }
}