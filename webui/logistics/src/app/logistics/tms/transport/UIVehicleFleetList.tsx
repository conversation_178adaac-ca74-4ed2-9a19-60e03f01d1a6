import React, { Component } from 'react';
import { util, grid, sql, bs, app, entity } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather';

import { T } from '../backend';

import { UIVehicleFleet, UIVehicleFleetEditor } from './UIVehicleFleet';
import { EditEmails } from '../utils';

export class UIVehicleFleetListPlugin extends entity.DbEntityListPlugin {

  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'VehicleFleetService',
      searchMethod: 'searchVehicleFleets',
      changeStorageStateMethod: 'changeVehicleFleetStorageState'
    }
    this.searchParams = {
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      orderBy: {
        fields: ["label", "modifiedTime"],
        fieldLabels: ["Label", "Modified Time"],
        selectFields: ['modifiedTime'],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call()
  }

  withOwnerLoginId(loginId: string) {
    this.addSearchParam("ownerLoginId", loginId);
    return this;
  }
}

interface UIVehicleFleetListProps extends entity.DbEntityListProps {
  ownerLoginId?: string
}

export class UIVehicleFleetList extends entity.DbEntityList<UIVehicleFleetListProps> {
  createVGridConfig() {
    let { appContext, pageContext, type } = this.props;
    let thisUI = this;
    let modCap = pageContext.hasUserModeratorCapability();
    let config: grid.VGridConfig = {
      title: 'Vehicle Fleets',
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Label'), 220),
          { name: 'code', label: T('Code'), state: { visible: false } },
          { name: 'index', label: T('Index') },
          { name: 'fleetType', label: T('Fleet Type') },
          {
            name: 'emails', label: T('Emails'), width: 210, state: { visible: true },
            customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
              let fleet = dRecord.record;
              let emails: any = fleet['emails'];
              let fileWidth = field.width ? field.width : 150;
              let openPopUpEmails = () => {
                appContext.createHttpBackendCall('VehicleFleetService', 'getVehicleFleetById', { id: fleet.id })
                  .withSuccessData((data: any) => {
                    let createPopupPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                      return (
                        <EditEmails appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(data)}
                          commitConfig={{ entityLabel: 'Fleet', service: 'VehicleFleetService', commitMethod: 'saveVehicleFleet' }}
                          onPostCommit={() => { thisUI.reloadData() }} />
                      )
                    }
                    pageContext.createPopupPage('edit-emails', T('Edit Emails'), createPopupPageContent, { size: 'sm', backdrop: "static" });
                  })
                  .call();
              }
              return (
                <div className={`flex-hbox align-items-center`}>
                  <div style={{ width: fileWidth - 22, overflow: 'hidden' }}>
                    <bs.Tooltip tooltip={emails} >{emails}</bs.Tooltip>
                  </div>
                  <FeatherIcon.Edit size={12} className='mx-1 state-modified' onClick={openPopUpEmails} />
                </div>
              )
            },
          },
          { name: 'description', label: T('Description'), width: 300 },
          { name: 'ownerLoginId', label: T('Owner Login Id'), width: 150, state: { visible: false } },
          { name: 'ownerLoginLabel', label: T('Owner Full Name'), width: 220 },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY
        ],
        summary: {
          dataCellHeight: 90,
          render: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord, _viewMode: grid.ViewMode) => {
            const infoField = ['fleetType', 'description', 'ownerLoginId', 'ownerLoginLabel'];
            let html = (
              <grid.SummaryCell className="flex-hbox" context={ctx} record={dRecord}>
                <grid.SummaryInfo context={ctx} record={dRecord} labelField="label" infoField={infoField}
                  descField="code" />
              </grid.SummaryCell>
            );
            return html;
          },
        },
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES(
            [entity.StorageState.ARCHIVED, entity.StorageState.ACTIVE], !modCap),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true)
      },

      footer: {
        page: {
          hide: type !== 'page',
          render: (ctx: grid.VGridContext) => {
            return <UIVehicleFleetListPageControl context={ctx} />;
          }
        },
      },

      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext } = this.props;
    let fleet = dRecord.record;
    let dataCallback = (data: any) => {
      let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UIVehicleFleet appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(data)} />
        );
      }
      pageContext.addPageContent('vehicle-fleet', T(`${fleet.label}`), createPageContent);
    };
    appContext
      .createHttpBackendCall('VehicleFleetService', 'getVehicleFleet', { code: fleet.code })
      .withSuccessData(dataCallback)
      .call()
  }
}

export class UIVehicleFleetListPageControl extends Component<grid.VGridContextProps> {
  onNew() {
    const { context } = this.props;
    const uiRoot = context.uiRoot as entity.DbEntityList<UIVehicleFleetListProps>;
    const { pageContext, ownerLoginId } = uiRoot.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let onPostCommit = (_entity: any, _uiEditor?: app.AppComponent) => {
      _uiEditor?.props.pageContext.back({ reload: true })
      uiRoot.reloadData();
    }
    let vehicleFleet = {
      code: `fleet-${util.TimeUtil.toDateTimeIdFormat(new Date())}`,
      label: 'New Vehicle Fleet',
      ownerLoginId,
      storageState: entity.StorageState.ACTIVE,
      editMode: entity.EditMode.VALIDATED,
    }
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return <UIVehicleFleetEditor
        appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(vehicleFleet)}
        readOnly={!writeCap} onPostCommit={onPostCommit} />
    }

    pageContext.createPopupPage('onNewVehicleFleet', T(`Vehicle Fleet : ${vehicleFleet.code}`), createAppPage, { size: 'md', backdrop: 'static' });
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { appContext, pageContext } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <bs.Toolbar className='border' hide={!writeCap}>
        <entity.WButtonEntityNew appContext={appContext} pageContext={pageContext}
          label={T('Vehicle Fleet')} onClick={() => this.onNew()} />
      </bs.Toolbar>
    );
  }
}